
	import MainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'
	
	type TestCase = { __$originalPosition?: UTSSourceMapPosition<"TestCase", "pages/test-datetime-picker.uvue", 43, 7>;
		label: string
		mode: string
		displayValue: string
		mapValue: Map<string, any> | null
	}
	
	const __sfc__ = defineComponent({
		name: "TestDatetimePicker",
		components: {
			MainDatetimePicker
		},
		data() {
			return {
				currentMode: 'datetime' as string,
				currentTitle: '选择时间' as string,
				currentTestIndex: -1 as number,
				testCases: [
					{
						label: '时间范围',
						mode: 'time-range',
						displayValue: '',
						mapValue: null
					},
					{
						label: '月份',
						mode: 'month',
						displayValue: '',
						mapValue: null
					},
					{
						label: '日期',
						mode: 'day',
						displayValue: '',
						mapValue: null
					},
					{
						label: '时间',
						mode: 'time',
						displayValue: '',
						mapValue: null
					},
					{
						label: '时分秒',
						mode: 'hour-minute-second',
						displayValue: '',
						mapValue: null
					},
					{
						label: '年份',
						mode: 'year',
						displayValue: '',
						mapValue: null
					},
					{
						label: '年月',
						mode: 'year-month',
						displayValue: '',
						mapValue: null
					}
				] as TestCase[]
			}
		},
		methods: {
			showPicker(mode: string, index: number) {
				this.currentMode = mode
				this.currentTestIndex = index
				this.currentTitle = this.testCases[index].label
				
				const picker = this.$refs['datetimePicker'] as ComponentPublicInstance
				picker.$callMethod('show')
			},
			
			onDateTimeConfirm(result: any) {
				console.log('选择结果:', result, " at pages/test-datetime-picker.uvue:117")
				
				if (this.currentTestIndex >= 0) {
					this.testCases[this.currentTestIndex].displayValue = result.formatted
					this.testCases[this.currentTestIndex].mapValue = result.value
				}
			},
			
			onDateTimeCancel() {
				console.log('取消选择', " at pages/test-datetime-picker.uvue:126")
			},
			
			formatMapValue(mapValue: Map<string, any> | Map<string, any>[] | null): string {
				if (!mapValue) return ''
				
				if (Array.isArray(mapValue)) {
					// 处理区间值
					return mapValue.map(map => this.formatSingleMap(map)).join(' | ')
				} else {
					// 处理单个值
					return this.formatSingleMap(mapValue)
				}
			},
			
			formatSingleMap(map: Map<string, any>): string {
				const parts: string[] = []
				
				if (map.has('year')) parts.push(`year:${map.get('year')}`)
				if (map.has('month')) parts.push(`month:${map.get('month')}`)
				if (map.has('day')) parts.push(`day:${map.get('day')}`)
				if (map.has('hour')) parts.push(`hour:${map.get('hour')}`)
				if (map.has('minute')) parts.push(`minute:${map.get('minute')}`)
				if (map.has('second')) parts.push(`second:${map.get('second')}`)
				
				return `{${parts.join(', ')}}`
			}
		}
	})

export default __sfc__
function GenPagesTestDatetimePickerRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_main_datetime_picker = resolveComponent("main-datetime-picker")

  return _cE("scroll-view", _uM({ class: "container" }), [
    _cE("view", _uM({ class: "page-container" }), [
      _cE("view", _uM({ class: "header" }), [
        _cE("text", _uM({ class: "title" }), "日期时间选择器测试")
      ]),
      _cE("view", _uM({ class: "test-section" }), [
        _cE(Fragment, null, RenderHelpers.renderList(_ctx.testCases, (test, index, __index, _cached): any => {
          return _cE("view", _uM({
            class: "test-item",
            key: index
          }), [
            _cE("view", _uM({ class: "test-label" }), [
              _cE("text", _uM({ class: "label-text" }), _tD(test.label), 1 /* TEXT */),
              _cE("text", _uM({ class: "mode-text" }), "模式: " + _tD(test.mode), 1 /* TEXT */)
            ]),
            _cE("view", _uM({
              class: "test-value",
              onClick: () => {_ctx.showPicker(test.mode, index)}
            }), [
              _cE("text", _uM({ class: "value-text" }), _tD(test.displayValue || '点击选择'), 1 /* TEXT */)
            ], 8 /* PROPS */, ["onClick"]),
            isTrue(test.mapValue)
              ? _cE("view", _uM({
                  key: 0,
                  class: "map-display"
                }), [
                  _cE("text", _uM({ class: "map-title" }), "Map值:"),
                  _cE("text", _uM({ class: "map-content" }), _tD(_ctx.formatMapValue(test.mapValue)), 1 /* TEXT */)
                ])
              : _cC("v-if", true)
          ])
        }), 128 /* KEYED_FRAGMENT */)
      ])
    ]),
    _cV(_component_main_datetime_picker, _uM({
      ref: "datetimePicker",
      mode: _ctx.currentMode,
      title: _ctx.currentTitle,
      onConfirm: _ctx.onDateTimeConfirm,
      onCancel: _ctx.onDateTimeCancel
    }), null, 8 /* PROPS */, ["mode", "title", "onConfirm", "onCancel"])
  ])
}
const GenPagesTestDatetimePickerStyles = [_uM([["container", _pS(_uM([["flex", 1], ["backgroundColor", "#f5f5f5"]]))], ["page-container", _pS(_uM([["paddingTop", "40rpx"], ["paddingRight", "40rpx"], ["paddingBottom", "40rpx"], ["paddingLeft", "40rpx"]]))], ["header", _pS(_uM([["marginBottom", "60rpx"], ["textAlign", "center"]]))], ["title", _pS(_uM([["fontSize", "36rpx"], ["fontWeight", "bold"], ["color", "#333333"]]))], ["test-section", _pS(_uM([["display", "flex"], ["flexDirection", "column"]]))], ["test-item", _pS(_uM([["backgroundColor", "#ffffff"], ["borderTopLeftRadius", "16rpx"], ["borderTopRightRadius", "16rpx"], ["borderBottomRightRadius", "16rpx"], ["borderBottomLeftRadius", "16rpx"], ["paddingTop", "30rpx"], ["paddingRight", "30rpx"], ["paddingBottom", "30rpx"], ["paddingLeft", "30rpx"], ["marginBottom", "20rpx"], ["boxShadow", "0 2rpx 8rpx rgba(0, 0, 0, 0.1)"]]))], ["test-label", _pS(_uM([["display", "flex"], ["flexDirection", "column"], ["marginBottom", "20rpx"]]))], ["label-text", _pS(_uM([["fontSize", "32rpx"], ["fontWeight", "bold"], ["color", "#333333"], ["marginBottom", "8rpx"]]))], ["mode-text", _pS(_uM([["fontSize", "24rpx"], ["color", "#666666"]]))], ["test-value", _pS(_uM([["backgroundColor", "#f8f9fa"], ["borderTopWidth", "2rpx"], ["borderRightWidth", "2rpx"], ["borderBottomWidth", "2rpx"], ["borderLeftWidth", "2rpx"], ["borderTopStyle", "solid"], ["borderRightStyle", "solid"], ["borderBottomStyle", "solid"], ["borderLeftStyle", "solid"], ["borderTopColor", "#e9ecef"], ["borderRightColor", "#e9ecef"], ["borderBottomColor", "#e9ecef"], ["borderLeftColor", "#e9ecef"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["paddingTop", "24rpx"], ["paddingRight", "24rpx"], ["paddingBottom", "24rpx"], ["paddingLeft", "24rpx"], ["marginBottom", "20rpx"]]))], ["value-text", _pS(_uM([["fontSize", "28rpx"], ["color", "#495057"]]))], ["map-display", _pS(_uM([["backgroundColor", "#e3f2fd"], ["borderTopLeftRadius", "8rpx"], ["borderTopRightRadius", "8rpx"], ["borderBottomRightRadius", "8rpx"], ["borderBottomLeftRadius", "8rpx"], ["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"]]))], ["map-title", _pS(_uM([["fontSize", "24rpx"], ["fontWeight", "bold"], ["color", "#1976d2"], ["marginBottom", "8rpx"]]))], ["map-content", _pS(_uM([["fontSize", "22rpx"], ["color", "#424242"], ["wordBreak", "break-all"]]))]])]
