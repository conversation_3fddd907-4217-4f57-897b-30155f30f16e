{"version": 3, "sources": ["pages/test-datetime-picker.uvue"], "sourcesContent": ["<template>\n\n\t<scroll-view class=\"container\">\n\n\t\t<view class=\"page-container\">\n\t\t\t<view class=\"header\">\n\t\t\t\t<text class=\"title\">日期时间选择器测试</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<view class=\"test-item\" v-for=\"(test, index) in testCases\" :key=\"index\">\n\t\t\t\t\t<view class=\"test-label\">\n\t\t\t\t\t\t<text class=\"label-text\">{{ test.label }}</text>\n\t\t\t\t\t\t<text class=\"mode-text\">模式: {{ test.mode }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"test-value\" @click=\"showPicker(test.mode, index)\">\n\t\t\t\t\t\t<text class=\"value-text\">{{ test.displayValue || '点击选择' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"test.mapValue\" class=\"map-display\">\n\t\t\t\t\t\t<text class=\"map-title\">Map值:</text>\n\t\t\t\t\t\t<text class=\"map-content\">{{ formatMapValue(test.mapValue) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 日期时间选择器 -->\n\t\t<main-datetime-picker \n\t\t\tref=\"datetimePicker\"\n\t\t\t:mode=\"currentMode\"\n\t\t\t:title=\"currentTitle\"\n\t\t\t@confirm=\"onDateTimeConfirm\"\n\t\t\t@cancel=\"onDateTimeCancel\"\n\t\t/>\n\n\t</scroll-view>\n\n</template>\n\n<script>\n\timport MainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'\n\t\n\ttype TestCase = {\n\t\tlabel: string\n\t\tmode: string\n\t\tdisplayValue: string\n\t\tmapValue: Map<string, any> | null\n\t}\n\t\n\texport default {\n\t\tname: \"TestDatetimePicker\",\n\t\tcomponents: {\n\t\t\tMainDatetimePicker\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentMode: 'datetime' as string,\n\t\t\t\tcurrentTitle: '选择时间' as string,\n\t\t\t\tcurrentTestIndex: -1 as number,\n\t\t\t\ttestCases: [\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '时间范围',\n\t\t\t\t\t\tmode: 'time-range',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '月份',\n\t\t\t\t\t\tmode: 'month',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '日期',\n\t\t\t\t\t\tmode: 'day',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '时间',\n\t\t\t\t\t\tmode: 'time',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '时分秒',\n\t\t\t\t\t\tmode: 'hour-minute-second',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '年份',\n\t\t\t\t\t\tmode: 'year',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '年月',\n\t\t\t\t\t\tmode: 'year-month',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t}\n\t\t\t\t] as TestCase[]\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tshowPicker(mode: string, index: number) {\n\t\t\t\tthis.currentMode = mode\n\t\t\t\tthis.currentTestIndex = index\n\t\t\t\tthis.currentTitle = this.testCases[index].label\n\t\t\t\t\n\t\t\t\tconst picker = this.$refs['datetimePicker'] as ComponentPublicInstance\n\t\t\t\tpicker.$callMethod('show')\n\t\t\t},\n\t\t\t\n\t\t\tonDateTimeConfirm(result: any) {\n\t\t\t\tconsole.log('选择结果:', result)\n\t\t\t\t\n\t\t\t\tif (this.currentTestIndex >= 0) {\n\t\t\t\t\tthis.testCases[this.currentTestIndex].displayValue = result.formatted\n\t\t\t\t\tthis.testCases[this.currentTestIndex].mapValue = result.value\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tonDateTimeCancel() {\n\t\t\t\tconsole.log('取消选择')\n\t\t\t},\n\t\t\t\n\t\t\tformatMapValue(mapValue: Map<string, any> | Map<string, any>[] | null): string {\n\t\t\t\tif (!mapValue) return ''\n\t\t\t\t\n\t\t\t\tif (Array.isArray(mapValue)) {\n\t\t\t\t\t// 处理区间值\n\t\t\t\t\treturn mapValue.map(map => this.formatSingleMap(map)).join(' | ')\n\t\t\t\t} else {\n\t\t\t\t\t// 处理单个值\n\t\t\t\t\treturn this.formatSingleMap(mapValue)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tformatSingleMap(map: Map<string, any>): string {\n\t\t\t\tconst parts: string[] = []\n\t\t\t\t\n\t\t\t\tif (map.has('year')) parts.push(`year:${map.get('year')}`)\n\t\t\t\tif (map.has('month')) parts.push(`month:${map.get('month')}`)\n\t\t\t\tif (map.has('day')) parts.push(`day:${map.get('day')}`)\n\t\t\t\tif (map.has('hour')) parts.push(`hour:${map.get('hour')}`)\n\t\t\t\tif (map.has('minute')) parts.push(`minute:${map.get('minute')}`)\n\t\t\t\tif (map.has('second')) parts.push(`second:${map.get('second')}`)\n\t\t\t\t\n\t\t\t\treturn `{${parts.join(', ')}}`\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #f5f5f5;\n\t}\n\t\n\t.page-container {\n\t\tpadding: 40rpx;\n\t}\n\t\n\t.header {\n\t\tmargin-bottom: 60rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.test-section {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.test-item {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.test-label {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.label-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.mode-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.test-value {\n\t\tbackground-color: #f8f9fa;\n\t\tborder: 2rpx solid #e9ecef;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.value-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #495057;\n\t}\n\t\n\t.map-display {\n\t\tbackground-color: #e3f2fd;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.map-title {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #1976d2;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.map-content {\n\t\tfont-size: 22rpx;\n\t\tcolor: #424242;\n\t\tword-break: break-all;\n\t}\n</style>\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAiDM;;;;;;;eA/CL,IAiCc,eAAA,IAjCD,WAAM,cAAW;YAE7B,IAoBO,QAAA,IApBD,WAAM,mBAAgB;gBAC3B,IAEO,QAAA,IAFD,WAAM,WAAQ;oBACnB,IAAoC,QAAA,IAA9B,WAAM,UAAQ;;gBAGrB,IAcO,QAAA,IAdD,WAAM,iBAAc;oBACzB,IAYO,UAAA,IAAA,EAAA,cAAA,UAAA,CAZyC,KAAA,SAAS,EAAA,IAAzB,MAAM,OAAN,SAAI,UAAA,GAAA,CAAA;+BAApC,IAYO,QAAA,IAZD,WAAM,aAAgD,SAAK;4BAChE,IAGO,QAAA,IAHD,WAAM,eAAY;gCACvB,IAAgD,QAAA,IAA1C,WAAM,eAAY,IAAI,KAAK,KAAK,GAAA,CAAA;gCACtC,IAAkD,QAAA,IAA5C,WAAM,cAAY,SAAI,IAAG,KAAK,IAAI,GAAA,CAAA;;4BAEzC,IAEO,QAAA,IAFD,WAAM,cAAc,aAAK,KAAA;gCAAE,KAAA,UAAU,CAAC,KAAK,IAAI,EAAE;4BAAK;;gCAC3D,IAAiE,QAAA,IAA3D,WAAM,eAAY,IAAI,KAAK,YAAY,IAAA,SAAA,CAAA;;;;uCAElC,KAAK,QAAQ;gCAAzB,IAGO,QAAA,gBAHoB,WAAM;oCAChC,IAAoC,QAAA,IAA9B,WAAM,cAAY;oCACxB,IAAoE,QAAA,IAA9D,WAAM,gBAAa,IAAI,KAAA,cAAc,CAAC,KAAK,QAAQ,IAAA,CAAA;;;;;;;;;;YAO7D,IAME,iCAAA,IALD,SAAI,kBACH,UAAM,KAAA,WAAW,EACjB,WAAO,KAAA,YAAY,EACnB,eAAS,KAAA,iBAAiB,EAC1B,cAAQ,KAAA,gBAAgB;;;;;;;;aAwBxB,aAA2B,MAAM;aACjC,cAAwB,MAAM;aAC9B,kBAAwB,MAAM;aAC9B,oBA2CK;;;mBA9CL,iBAAa,WAAS,EAAA,CAAK,MAAM,EACjC,kBAAc,OAAK,EAAA,CAAK,MAAM,EAC9B,sBAAkB,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM,EAC9B,eAAW,IA2CN,mBAzCH,QAAO,QACP,OAAM,cACN,eAAc,IACd,WAAU,IAAG,YAGb,QAAO,MACP,OAAM,SACN,eAAc,IACd,WAAU,IAAG,YAGb,QAAO,MACP,OAAM,OACN,eAAc,IACd,WAAU,IAAG,YAGb,QAAO,MACP,OAAM,QACN,eAAc,IACd,WAAU,IAAG,YAGb,QAAO,OACP,OAAM,sBACN,eAAc,IACd,WAAU,IAAG,YAGb,QAAO,MACP,OAAM,QACN,eAAc,IACd,WAAU,IAAG,YAGb,QAAO,MACP,OAAM,cACN,eAAc,IACd,WAAU,IAAG;;aAMhB;aAAA,kBAAW,MAAM,MAAM,EAAE,OAAO,MAAM,EAAA;QACrC,IAAI,CAAC,WAAU,GAAI;QACnB,IAAI,CAAC,gBAAe,GAAI;QACxB,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAI;QAE9C,IAAM,SAAS,IAAI,CAAC,OAAK,CAAC,iBAAgB,CAAA,EAAA,CAAK;QAC/C,OAAO,aAAW,CAAC;IACpB;aAEA;aAAA,yBAAkB,QAAQ,GAAG,EAAA;QAC5B,QAAQ,GAAG,CAAC,SAAS,QAAM;QAE3B,IAAI,IAAI,CAAC,gBAAe,IAAK,CAAC,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAW,GAAI,OAAO,SAAQ;YACpE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAO,GAAI,OAAO,KAAI;;IAE9D;aAEA;aAAA,0BAAgB;QACf,QAAQ,GAAG,CAAC,QAAM;IACnB;aAEA,eAAe,cAAsD,GAAG,MAAK,CAAA;QAC5E,IAAI,CAAC;YAAU,OAAO;;QAEtB,IAAI,SAAM,OAAO,CAAC,WAAW;YAE5B,OAAO,CAAA,SAAQ,EAAA,UAAA,IAAA,MAAA,EAAA,GAAA,EAAA,EAAC,GAAG,CAAC,IAAA,MAAE,MAAA;uBAAK,IAAI,CAAC,eAAe,CAAC;eAAM,IAAI,CAAC;eACrD;YAEN,OAAO,IAAI,CAAC,eAAe,CAAC,SAAQ,EAAA,CAAA,IAAA,MAAA,EAAA,GAAA;;IAEtC;aAEA;aAAA,uBAAgB,KAAK,IAAI,MAAM,EAAE,GAAG,CAAC,GAAG,MAAK,CAAA;QAC5C,IAAM,gBAAO,MAAM,IAAK,KAAC;QAEzB,IAAI,IAAI,GAAG,CAAC;YAAS,MAAM,IAAI,CAAC,UAAQ,IAAI,GAAG,CAAC;;QAChD,IAAI,IAAI,GAAG,CAAC;YAAU,MAAM,IAAI,CAAC,WAAS,IAAI,GAAG,CAAC;;QAClD,IAAI,IAAI,GAAG,CAAC;YAAQ,MAAM,IAAI,CAAC,SAAO,IAAI,GAAG,CAAC;;QAC9C,IAAI,IAAI,GAAG,CAAC;YAAS,MAAM,IAAI,CAAC,UAAQ,IAAI,GAAG,CAAC;;QAChD,IAAI,IAAI,GAAG,CAAC;YAAW,MAAM,IAAI,CAAC,YAAU,IAAI,GAAG,CAAC;;QACpD,IAAI,IAAI,GAAG,CAAC;YAAW,MAAM,IAAI,CAAC,YAAU,IAAI,GAAG,CAAC;;QAEpD,OAAO,MAAI,MAAM,IAAI,CAAC,QAAK;IAC5B;;mBArGK;;;;;;;;;;;;;;;;;;;AAuGP"}