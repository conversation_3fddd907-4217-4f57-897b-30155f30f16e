{"version": 3, "sources": ["components/main-form/tools/main-datetime-picker.uvue", "App.uvue"], "sourcesContent": ["<template>\n\t<!-- 弹窗遮罩层 -->\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\n\t\t\t<view class=\"datetime-picker-container\">\n\t\t\t\t<!-- 导航栏 -->\n\t\t\t\t<view class=\"navbar\">\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\n\t\t\t\t\t<text class=\"nav-title\">{{ displayTitle }}</text>\n\t\t\t\t\t<view class=\"confirm-btn-container\">\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快捷选项 -->\n\t\t\t\t<view v-if=\"quickOptions.length > 0\" class=\"quick-options-container\">\n\t\t\t\t\t<scroll-view direction=\"horizontal\" class=\"quick-options-scroll\" show-scrollbar=\"false\">\n\t\t\t\t\t\t<view class=\"quick-options\">\n\t\t\t\t\t\t\t<text v-for=\"(option, index) in quickOptions\" :key=\"index\" class=\"quick-item\"\n\t\t\t\t\t\t\t\t:class=\"{ 'quick-item-active': currentQuickIndex == index }\" @click=\"onQuickSelectByIndex(index)\">\n\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 区间选择标签 -->\n\t\t\t\t<view v-if=\"isRange\" class=\"range-tabs\">\n\t\t\t\t\t<text class=\"range-tab\" :class=\"{ 'range-tab-active': rangeIndex == 0 }\" @click=\"onRangeChange(0)\">\n\t\t\t\t\t\t开始时间\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"range-tab\" :class=\"{ 'range-tab-active': rangeIndex == 1 }\" @click=\"onRangeChange(1)\">\n\t\t\t\t\t\t结束时间\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- picker-view 选择器 -->\n\t\t\t\t<view class=\"picker-body\">\n\t\t\t\t\t<picker-view :value=\"pickerValue\" @change=\"onPickerChange\" class=\"picker-view\"\n\t\t\t\t\t\t:indicator-style=\"indicatorStyle\" :style=\"{ height: `${height}px` }\">\n\t\t\t\t\t\t<picker-view-column v-if=\"showYear\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"year in years\" :key=\"year\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ year }}年</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showMonth\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"month in months\" :key=\"month\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ month }}月</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showDay\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"day in days\" :key=\"day\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ day }}日</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showHour\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"hour in hours\" :key=\"hour\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ hour }}时</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showMinute\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"minute in minutes\" :key=\"minute\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ minute }}分</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showSecond\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"second in seconds\" :key=\"second\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ second }}秒</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t</picker-view>\n\t\t\t\t</view>\n\n\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { DateQuickOption} from '@/components/main-form/form_type.uts'\n\n\t// 定义模式类型\n\ttype DateTimeMode = 'datetime' | 'date' | 'time' | 'year' | 'year-month' | 'month' | 'day' | 'hour-minute' | 'hour-minute-second' | 'datetime-range' | 'date-range' | 'time-range'\n\n\texport default {\n\t\tname: \"main-datetime-picker\",\n\t\temits: ['cancel', 'confirm', 'change'],\n\t\tprops: {\n\t\t\t// 选择模式\n\t\t\tmode: {\n\t\t\t\ttype: String as PropType<DateTimeMode>,\n\t\t\t\tdefault: 'datetime' as DateTimeMode\n\t\t\t},\n\t\t\t// 标题\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '选择时间'\n\t\t\t},\n\t\t\t// 是否显示秒\n\t\t\tshowSeconds: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 开始年份\n\t\t\tstartYear: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: () =>1970\n\t\t\t},\n\t\t\t// 结束年份\n\t\t\tendYear: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: () => 2035\n\t\t\t},\n\t\t\t// 快捷选项\n\t\t\tquickOptions: {\n\t\t\t\ttype: Array as PropType<DateQuickOption[]>,\n\t\t\t\tdefault: () => [] as DateQuickOption[]\n\t\t\t},\n\t\t\t// 选择器高度\n\t\t\theight: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 264\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\tconst now: Date = new Date()\n\t\t\treturn {\n\t\t\t\t// 控制弹窗显示\n\t\t\t\tvisible: false as boolean,\n\t\t\t\t// 当前日期\n\t\t\t\tcurrentDate: now as Date,\n\t\t\t\t// 区间值\n\t\t\t\trangeValues: [now, now] as Date[],\n\t\t\t\t// 当前区间索引\n\t\t\t\trangeIndex: 0 as number,\n\t\t\t\t// picker-view 的值\n\t\t\t\tpickerValue: [] as number[],\n\t\t\t\t// 当前快捷选项索引\n\t\t\t\tcurrentQuickIndex: -1 as number,\n\t\t\t\t// 年份列表\n\t\t\t\tyears: [] as string[],\n\t\t\t\t// 月份列表\n\t\t\t\tmonths: [] as string[],\n\t\t\t\t// 日期列表\n\t\t\t\tdays: [] as string[],\n\t\t\t\t// 小时列表\n\t\t\t\thours: [] as string[],\n\t\t\t\t// 分钟列表\n\t\t\t\tminutes: [] as string[],\n\t\t\t\t// 秒列表\n\t\t\t\tseconds: [] as string[],\n\t\t\t\t// 是否已初始化\n\t\t\t\tisInitialized: false as boolean\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 是否为区间模式\n\t\t\tisRange(): boolean {\n\t\t\t\treturn this.mode.includes('range')\n\t\t\t},\n\t\t\t// 是否显示年份\n\t\t\tshowYear(): boolean {\n\t\t\t\treturn ['datetime', 'date', 'year', 'year-month'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'date-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示月份\n\t\t\tshowMonth(): boolean {\n\t\t\t\treturn ['datetime', 'date', 'year-month', 'month'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'date-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示日期\n\t\t\tshowDay(): boolean {\n\t\t\t\treturn ['datetime', 'date', 'day'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'date-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示小时\n\t\t\tshowHour(): boolean {\n\t\t\t\treturn ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'time-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示分钟\n\t\t\tshowMinute(): boolean {\n\t\t\t\treturn ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'time-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示秒\n\t\t\tshowSecond(): boolean {\n\t\t\t\treturn ['hour-minute-second'].includes(this.mode) ||\n\t\t\t\t\t   (this.showSeconds && ['datetime', 'time'].includes(this.mode)) ||\n\t\t\t\t\t   (this.showSeconds && ['datetime-range', 'time-range'].includes(this.mode))\n\t\t\t},\n\t\t\t// 指示器样式\n\t\t\tindicatorStyle(): string {\n\t\t\t\treturn 'height: 44px; border-top: 1px solid #eee; border-bottom: 1px solid #eee;'\n\t\t\t},\n\t\t\t// 显示标题\n\t\t\tdisplayTitle(): string {\n\t\t\t\tif (this.title != '选择时间') return this.title\n\n\t\t\t\tconst modeMap: UTSJSONObject = {\n\t\t\t\t\t'datetime': '选择日期时间',\n\t\t\t\t\t'date': '选择日期',\n\t\t\t\t\t'time': '选择时间',\n\t\t\t\t\t'year': '选择年份',\n\t\t\t\t\t'year-month': '选择年月',\n\t\t\t\t\t'month': '选择月份',\n\t\t\t\t\t'day': '选择日期',\n\t\t\t\t\t'hour-minute': '选择时间',\n\t\t\t\t\t'hour-minute-second': '选择时间',\n\t\t\t\t\t'datetime-range': '选择日期时间范围',\n\t\t\t\t\t'date-range': '选择日期范围',\n\t\t\t\t\t'time-range': '选择时间范围'\n\t\t\t\t}\n\n\t\t\t\tconst result = modeMap[this.mode] as string | null\n\t\t\t\treturn result != null ? result : '选择时间'\n\t\t\t},\n\t\t\t// 当前显示值\n\t\t\tcurrentDisplayValue(): string {\n\t\t\t\tif (this.isRange) {\n\t\t\t\t\tconst startFormatted: string = this.formatDate(this.rangeValues[0], this.mode.replace('-range', '') as DateTimeMode)\n\t\t\t\t\tconst endFormatted: string = this.formatDate(this.rangeValues[1], this.mode.replace('-range', '') as DateTimeMode)\n\t\t\t\t\treturn startFormatted + ' 至 ' + endFormatted\n\t\t\t\t} else {\n\t\t\t\t\treturn this.formatDate(this.currentDate, this.mode)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.initData()\n\t\t},\n\t\tmethods: {\n\t\t\t// 格式化日期\n\t\t\tformatDate(date: Date | null, type: DateTimeMode): string {\n\t\t\t\tif (date == null) return ''\n\n\t\t\t\ttry {\n\t\t\t\t\t// 确保是 Date 对象\n\t\t\t\t\tconst d: Date = date\n\t\t\t\t\tif (!this.validateDate(d)) return ''\n\n\t\t\t\t\tconst year: number = d.getFullYear()\n\t\t\t\t\tconst month: string = (d.getMonth() + 1).toString().padStart(2, '0')\n\t\t\t\t\tconst day: string = d.getDate().toString().padStart(2, '0')\n\t\t\t\t\tconst hour: string = d.getHours().toString().padStart(2, '0')\n\t\t\t\t\tconst minute: string = d.getMinutes().toString().padStart(2, '0')\n\t\t\t\t\tconst second: string = d.getSeconds().toString().padStart(2, '0')\n\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase 'datetime':\n\t\t\t\t\t\t\treturn `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`\n\t\t\t\t\t\tcase 'date':\n\t\t\t\t\t\t\treturn `${year}-${month}-${day}`\n\t\t\t\t\t\tcase 'time':\n\t\t\t\t\t\t\treturn `${hour}:${minute}${this.showSeconds ? ':' + second : ''}`\n\t\t\t\t\t\tcase 'year':\n\t\t\t\t\t\t\treturn `${year}`\n\t\t\t\t\t\tcase 'year-month':\n\t\t\t\t\t\t\treturn `${year}-${month}`\n\t\t\t\t\t\tcase 'month':\n\t\t\t\t\t\t\treturn `${month}`\n\t\t\t\t\t\tcase 'day':\n\t\t\t\t\t\t\treturn `${day}`\n\t\t\t\t\t\tcase 'hour-minute':\n\t\t\t\t\t\t\treturn `${hour}:${minute}`\n\t\t\t\t\t\tcase 'hour-minute-second':\n\t\t\t\t\t\t\treturn `${hour}:${minute}:${second}`\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Format date error:', error, date)\n\t\t\t\t\treturn ''\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 解析日期\n\t\t\tparseDate(value: Date | string | number | null): Date {\n\t\t\t\tif (value == null) return new Date()\n\n\t\t\t\ttry {\n\t\t\t\t\tlet date: Date | null = null\n\t\t\t\t\tif (value instanceof Date) {\n\t\t\t\t\t\t// 如果已经是 Date 对象，创建一个新的副本\n\t\t\t\t\t\tdate = new Date(value.getTime())\n\t\t\t\t\t} else if (typeof value == 'number' && !isNaN(value)) {\n\t\t\t\t\t\t// 数字类型，作为时间戳处理\n\t\t\t\t\t\tdate = new Date(value as number)\n\t\t\t\t\t} else if (typeof value == 'string') {\n\t\t\t\t\t\t// 字符串类型，需要解析\n\t\t\t\t\t\tif (value.includes('T')) {\n\t\t\t\t\t\t\t// ISO 格式字符串\n\t\t\t\t\t\t\tdate = new Date(value as string)\n\t\t\t\t\t\t} else if (value.includes('-') || value.includes('/')) {\n\t\t\t\t\t\t\t// 自定义格式字符串，手动解析\n\t\t\t\t\t\t\tconst parts: number[] = value.split(/[-\\s:/]/).map(p => parseInt(p))\n\t\t\t\t\t\t\tif (parts.length >= 3) {\n\t\t\t\t\t\t\t\tdate = new Date(\n\t\t\t\t\t\t\t\t\tparts[0], // year\n\t\t\t\t\t\t\t\t\tparts[1] - 1, // month\n\t\t\t\t\t\t\t\t\tparts[2], // day\n\t\t\t\t\t\t\t\t\tparts.length > 3 ? parts[3] : 0, // hour\n\t\t\t\t\t\t\t\t\tparts.length > 4 ? parts[4] : 0, // minute\n\t\t\t\t\t\t\t\t\tparts.length > 5 ? parts[5] : 0 // second\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 尝试作为时间戳字符串解析\n\t\t\t\t\t\t\tconst timestamp: number = parseInt(value)\n\t\t\t\t\t\t\tif (!isNaN(timestamp)) {\n\t\t\t\t\t\t\t\tdate = new Date(timestamp as number)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn date != null && !isNaN(date.getTime()) ? date : new Date()\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Parse date error:', error)\n\t\t\t\t\treturn new Date()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 验证日期\n\t\t\tvalidateDate(date: Date): boolean {\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\tconsole.warn('Invalid date:', date)\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tconst year: number = date.getFullYear()\n\t\t\t\tif (year < this.startYear || year > this.endYear) {\n\t\t\t\t\tconsole.warn('Date out of range:', date)\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshow(value?: Date | Date[] | string | number) {\n\t\t\t\tthis.visible = true\n\t\t\t\tthis.currentQuickIndex = -1\n\t\t\t\tthis.rangeIndex = 0\n\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\t// 处理区间值\n\t\t\t\t\t\tif (Array.isArray(value) && value.length == 2) {\n\t\t\t\t\t\t\tthis.rangeValues = value.map(v => this.parseDate(v))\n\t\t\t\t\t\t} else if (typeof value == 'string') {\n\t\t\t\t\t\t\t// 尝试解析字符串格式的日期\n\t\t\t\t\t\t\tconst date: Date = this.parseDate(value)\n\t\t\t\t\t\t\tthis.rangeValues = [date, date]\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst now: Date = new Date()\n\t\t\t\t\t\t\tthis.rangeValues = [now, now]\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 处理单个值\n\t\t\t\t\t\tthis.currentDate = this.parseDate(value)\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.initData()\n\t\t\t\t\t\tthis.updateCurrentValue()\n\t\t\t\t\t})\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Show picker error:', error)\n\t\t\t\t\tconst now: Date = new Date()\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\tthis.rangeValues = [now, now]\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.currentDate = now\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 隐藏选择器\n\t\t\thide() {\n\t\t\t\tthis.visible = false\n\t\t\t},\n\n\t\t\t// 点击遮罩层关闭弹窗\n\t\t\tonOverlayClick() {\n\t\t\t\tthis.hide()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 取消按钮点击事件\n\t\t\tonCancel() {\n\t\t\t\tthis.hide()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 创建值的Map对象\n\t\t\tcreateValueMap(date: Date): Map<string, any> {\n\t\t\t\tconst map = new Map<string, any>()\n\n\t\t\t\tif (this.showYear) {\n\t\t\t\t\tmap.set('year', date.getFullYear())\n\t\t\t\t}\n\t\t\t\tif (this.showMonth) {\n\t\t\t\t\tmap.set('month', date.getMonth() + 1)\n\t\t\t\t}\n\t\t\t\tif (this.showDay) {\n\t\t\t\t\tmap.set('day', date.getDate())\n\t\t\t\t}\n\t\t\t\tif (this.showHour) {\n\t\t\t\t\tmap.set('hour', date.getHours())\n\t\t\t\t}\n\t\t\t\tif (this.showMinute) {\n\t\t\t\t\tmap.set('minute', date.getMinutes())\n\t\t\t\t}\n\t\t\t\tif (this.showSecond) {\n\t\t\t\t\tmap.set('second', date.getSeconds())\n\t\t\t\t}\n\n\t\t\t\treturn map\n\t\t\t},\n\n\t\t\t// 确定按钮点击事件\n\t\t\tonConfirm() {\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\tif (!this.validateDate(this.rangeValues[0]) || !this.validateDate(this.rangeValues[1])) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '日期格式无效',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (this.rangeValues[1] < this.rangeValues[0]) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '结束时间不能早于开始时间',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst dateValues: Date[] = this.rangeValues.map(date => new Date(date.getTime()))\n\t\t\t\t\t\tconst mapValues: Map<string, any>[] = dateValues.map(date => this.createValueMap(date))\n\t\t\t\t\t\tconst formatted: string = dateValues.map(date => this.formatDate(date, this.mode.replace('-range', '') as DateTimeMode)).join(' 至 ')\n\n\t\t\t\t\t\tthis.$emit('confirm', {\n\t\t\t\t\t\t\tvalue: mapValues,\n\t\t\t\t\t\t\tformatted\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (!this.validateDate(this.currentDate)) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '日期格式无效',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst dateValue: Date = new Date(this.currentDate.getTime())\n\t\t\t\t\t\tconst mapValue: Map<string, any> = this.createValueMap(dateValue)\n\t\t\t\t\t\tconst formatted: string = this.formatDate(dateValue, this.mode)\n\n\t\t\t\t\t\tthis.$emit('confirm', {\n\t\t\t\t\t\t\tvalue: mapValue,\n\t\t\t\t\t\t\tformatted\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.hide()\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Confirm error:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 通过索引选择快捷选项\n\t\t\tonQuickSelectByIndex(index: number) {\n\t\t\t\tif (index < 0 || index >= this.quickOptions.length) {\n\t\t\t\t\tconsole.warn('Invalid quick option index:', index)\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst option: DateQuickOption = this.quickOptions[index]\n\t\t\t\tthis.onQuickSelect(option, index)\n\t\t\t},\n\n\t\t\t// 快捷选项选择\n\t\t\tonQuickSelect(option: DateQuickOption, index: number) {\n\t\t\t\tthis.currentQuickIndex = index\n\t\t\t\tthis.rangeIndex = 0\n\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\t// 处理区间值\n\t\t\t\t\t\tif (Array.isArray(option.value)) {\n\t\t\t\t\t\t\t// Date 数组\n\t\t\t\t\t\t\tconst rangeValue: Date[] = option.value as Date[]\n\t\t\t\t\t\t\tif (rangeValue.length != 2) {\n\t\t\t\t\t\t\t\tconsole.warn('Quick option value should have 2 items for range mode:', option)\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.rangeValues = rangeValue.map(v => this.parseDate(v))\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 单个 Date，转换为区间\n\t\t\t\t\t\t\tconst date: Date = this.parseDate(option.value as Date)\n\t\t\t\t\t\t\tthis.rangeValues = [date, date]\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 处理单个值\n\t\t\t\t\t\tif (Array.isArray(option.value)) {\n\t\t\t\t\t\t\t// 如果是数组，取第一个值\n\t\t\t\t\t\t\tthis.currentDate = this.parseDate((option.value as Date[])[0])\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 单个 Date 值\n\t\t\t\t\t\t\tthis.currentDate = this.parseDate(option.value as Date)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.initData()\n\t\t\t\t\t\tthis.updateCurrentValue()\n\t\t\t\t\t})\n\n\t\t\t\t\tif (option.autoConfirm == true) {\n\t\t\t\t\t\tthis.onConfirm()\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Quick select error:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '快捷选项格式无效',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// picker-view 变化事件\n\t\t\tonPickerChange(e: UniPickerViewChangeEvent) {\n\t\t\t\tthis.pickerValue = e.detail.value\n\t\t\t\tthis.currentQuickIndex = -1\n\t\t\t\tthis.updateDateFromValue()\n\t\t\t},\n\n\t\t\t// 区间选择切换\n\t\t\tonRangeChange(index: number) {\n\t\t\t\tif (this.rangeIndex == index) return\n\t\t\t\tthis.rangeIndex = index\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.updateCurrentValue()\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 初始化数据\n\t\t\tinitData() {\n\t\t\t\tif (!this.isInitialized) {\n\t\t\t\t\t// 年\n\t\t\t\t\tthis.years = []\n\t\t\t\t\tfor (let i: number = this.startYear; i <= this.endYear; i++) {\n\t\t\t\t\t\tthis.years.push(i.toString())\n\t\t\t\t\t}\n\n\t\t\t\t\t// 月\n\t\t\t\t\tthis.months = []\n\t\t\t\t\tfor (let i: number = 1; i <= 12; i++) {\n\t\t\t\t\t\tthis.months.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 时\n\t\t\t\t\tthis.hours = []\n\t\t\t\t\tfor (let i: number = 0; i <= 23; i++) {\n\t\t\t\t\t\tthis.hours.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 分\n\t\t\t\t\tthis.minutes = []\n\t\t\t\t\tfor (let i: number = 0; i <= 59; i++) {\n\t\t\t\t\t\tthis.minutes.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 秒\n\t\t\t\t\tthis.seconds = []\n\t\t\t\t\tfor (let i: number = 0; i <= 59; i++) {\n\t\t\t\t\t\tthis.seconds.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.isInitialized = true\n\t\t\t\t}\n\n\t\t\t\t// 更新日期列表\n\t\t\t\tthis.updateDaysList()\n\t\t\t},\n\n\t\t\t// 更新日期列表（根据当前年月动态计算）\n\t\t\tupdateDaysList() {\n\t\t\t\tconst date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate\n\t\t\t\tconst year: number = date.getFullYear()\n\t\t\t\tconst month: number = date.getMonth() + 1\n\n\t\t\t\t// 获取当月天数，处理闰年和不同月份的天数差异\n\t\t\t\tconst daysInMonth: number = this.getDaysInMonth(year, month)\n\n\t\t\t\tthis.days = []\n\t\t\t\tfor (let i: number = 1; i <= daysInMonth; i++) {\n\t\t\t\t\tthis.days.push(i.toString().padStart(2, '0'))\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取指定年月的天数\n\t\t\tgetDaysInMonth(year: number, month: number): number {\n\t\t\t\t// 使用 Date 构造函数的特性：月份设为下个月的第0天，会自动返回当月最后一天\n\t\t\t\treturn new Date(year, month, 0).getDate()\n\t\t\t},\n\n\t\t\t// 更新当前值\n\t\t\tupdateCurrentValue() {\n\t\t\t\tconst date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\tconsole.warn('Invalid date in updateCurrentValue:', date)\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 确保日期列表是最新的\n\t\t\t\tthis.updateDaysList()\n\n\t\t\t\tconst values: number[] = []\n\n\t\t\t\tif (this.showYear) {\n\t\t\t\t\tconst yearIndex: number = this.years.findIndex(y => parseInt(y) == date.getFullYear())\n\t\t\t\t\tvalues.push(yearIndex >= 0 ? yearIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showMonth) {\n\t\t\t\t\tconst monthStr: string = (date.getMonth() + 1).toString().padStart(2, '0')\n\t\t\t\t\tconst monthIndex: number = this.months.findIndex(m => m == monthStr)\n\t\t\t\t\tvalues.push(monthIndex >= 0 ? monthIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showDay) {\n\t\t\t\t\tconst dayStr: string = date.getDate().toString().padStart(2, '0')\n\t\t\t\t\tconst dayIndex: number = this.days.findIndex(d => d == dayStr)\n\t\t\t\t\t// 确保索引在有效范围内\n\t\t\t\t\tif (dayIndex >= 0 && dayIndex < this.days.length) {\n\t\t\t\t\t\tvalues.push(dayIndex)\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果找不到对应的日期，使用当月最后一天的索引\n\t\t\t\t\t\tvalues.push(this.days.length - 1)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (this.showHour) {\n\t\t\t\t\tconst hourStr: string = date.getHours().toString().padStart(2, '0')\n\t\t\t\t\tconst hourIndex: number = this.hours.findIndex(h => h == hourStr)\n\t\t\t\t\tvalues.push(hourIndex >= 0 ? hourIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showMinute) {\n\t\t\t\t\tconst minuteStr: string = date.getMinutes().toString().padStart(2, '0')\n\t\t\t\t\tconst minuteIndex: number = this.minutes.findIndex(m => m == minuteStr)\n\t\t\t\t\tvalues.push(minuteIndex >= 0 ? minuteIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showSecond) {\n\t\t\t\t\tconst secondStr: string = date.getSeconds().toString().padStart(2, '0')\n\t\t\t\t\tconst secondIndex: number = this.seconds.findIndex(s => s == secondStr)\n\t\t\t\t\tvalues.push(secondIndex >= 0 ? secondIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tthis.pickerValue = [...values]\n\t\t\t},\n\n\t\t\t// 从picker值更新日期\n\t\t\tupdateDateFromValue() {\n\t\t\t\tif (!Array.isArray(this.pickerValue)) return\n\n\t\t\t\tlet index: number = 0\n\t\t\t\tlet year: number = this.currentDate.getFullYear()\n\t\t\t\tlet month: number = this.currentDate.getMonth()\n\t\t\t\tlet day: number = this.currentDate.getDate()\n\t\t\t\tlet hour: number = this.currentDate.getHours()\n\t\t\t\tlet minute: number = this.currentDate.getMinutes()\n\t\t\t\tlet second: number = this.currentDate.getSeconds()\n\n\t\t\t\t// 记录原始的年月，用于检测是否发生变化\n\t\t\t\tconst originalYear: number = year\n\t\t\t\tconst originalMonth: number = month\n\n\t\t\t\tif (this.showYear && index < this.pickerValue.length) {\n\t\t\t\t\tyear = parseInt(this.years[this.pickerValue[index]])\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showMonth && index < this.pickerValue.length) {\n\t\t\t\t\tmonth = parseInt(this.months[this.pickerValue[index]]) - 1\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\t// 检查年月是否发生变化，如果是，需要重新计算日期列表\n\t\t\t\tconst yearMonthChanged: boolean = (year != originalYear || month != originalMonth)\n\n\t\t\t\tif (yearMonthChanged) {\n\t\t\t\t\t// 年月发生变化，需要重新计算当月天数\n\t\t\t\t\tconst daysInNewMonth: number = this.getDaysInMonth(year, month + 1)\n\n\t\t\t\t\t// 如果当前日期超过了新月份的最大天数，调整为最大天数\n\t\t\t\t\tif (day > daysInNewMonth) {\n\t\t\t\t\t\tday = daysInNewMonth\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新日期列表\n\t\t\t\t\tthis.days = []\n\t\t\t\t\tfor (let i: number = 1; i <= daysInNewMonth; i++) {\n\t\t\t\t\t\tthis.days.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新 picker-view 的日期索引，确保在有效范围内\n\t\t\t\t\tif (this.showDay && index < this.pickerValue.length) {\n\t\t\t\t\t\tconst currentDayIndex: number = this.pickerValue[index]\n\t\t\t\t\t\tif (currentDayIndex >= this.days.length) {\n\t\t\t\t\t\t\t// 如果当前索引超出新月份的天数范围，调整为最后一天\n\t\t\t\t\t\t\tthis.pickerValue[index] = this.days.length - 1\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (this.showDay && index < this.pickerValue.length) {\n\t\t\t\t\tconst dayIndex: number = this.pickerValue[index]\n\t\t\t\t\t// 检查索引是否在有效范围内\n\t\t\t\t\tif (dayIndex >= 0 && dayIndex < this.days.length) {\n\t\t\t\t\t\tconst selectedDay: number = parseInt(this.days[dayIndex])\n\t\t\t\t\t\tday = selectedDay\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果索引超出范围，使用当月最后一天\n\t\t\t\t\t\tday = this.days.length\n\t\t\t\t\t}\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showHour && index < this.pickerValue.length) {\n\t\t\t\t\thour = parseInt(this.hours[this.pickerValue[index]])\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showMinute && index < this.pickerValue.length) {\n\t\t\t\t\tminute = parseInt(this.minutes[this.pickerValue[index]])\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showSecond && index < this.pickerValue.length) {\n\t\t\t\t\tsecond = parseInt(this.seconds[this.pickerValue[index]])\n\t\t\t\t}\n\n\t\t\t\tconst newDate: Date = new Date(year, month, day, hour, minute, second)\n\n\t\t\t\tif (this.isRange) {\n\t\t\t\t\tthis.rangeValues[this.rangeIndex] = newDate\n\t\t\t\t\tif (this.rangeIndex == 0 && this.rangeValues[1] < newDate) {\n\t\t\t\t\t\tthis.rangeValues[1] = new Date(newDate.getTime())\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentDate = newDate\n\t\t\t\t}\n\n\t\t\t\t// 如果年月没有变化，只需要更新日期列表\n\t\t\t\tif (!yearMonthChanged) {\n\t\t\t\t\tthis.updateDaysList()\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t</script>\n\n\t<style>\n\t\t/* 遮罩层 */\n\t\t.picker-overlay {\n\t\t\tposition: fixed;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground-color: rgba(0, 0, 0, 0.4);\n\t\t\tz-index: 999;\n\t\t}\n\n\t\t.picker-modal {\n\t\t\tposition: fixed;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground-color: #ffffff;\n\t\t\tz-index: 1000;\n\t\t}\n\n\t\t.datetime-picker-container {\n\t\t\twidth: 100%;\n\t\t\tbackground-color: #ffffff;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t}\n\n\t\t/* 导航栏样式 */\n\t\t.navbar {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tflex-direction: row;\n\t\t\tjustify-content: space-between;\n\t\t\theight: 88rpx;\n\t\t\tpadding: 0 30rpx;\n\t\t\tbackground-color: #ffffff;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\n\t\t.nav-btn {\n\t\t\tmin-width: 80rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.cancel-btn {\n\t\t\tcolor: #999;\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t.confirm-btn-container {\n\t\t\tmin-width: 80rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.confirm-btn {\n\t\t\tcolor: #576B95;\n\t\t\tfont-weight: 400;\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t.nav-title {\n\t\t\tcolor: #333;\n\t\t\tfont-weight: 400;\n\t\t\tfont-size: 32rpx;\n\t\t\ttext-align: center;\n\t\t\tflex: 1;\n\t\t}\n\n\t\t/* 快捷选项样式 */\n\t\t.quick-options-container {\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\n\t\t.quick-options-scroll {\n\t\t\theight: 80rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.quick-options {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tpadding: 10rpx 20rpx;\n\t\t\theight: 60rpx;\n\t\t}\n\n\t\t.quick-item {\n\t\t\tpadding: 8rpx 16rpx;\n\t\t\tmargin-right: 12rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t\tbackground-color: #f5f5f5;\n\t\t\tborder-radius: 20rpx;\n\t\t\twhite-space: nowrap;\n\t\t}\n\n\t\t.quick-item-active {\n\t\t\tcolor: #ffffff;\n\t\t\tbackground-color: #007AFF;\n\t\t}\n\n\t\t/* 区间选择标签样式 */\n\t\t.range-tabs {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\n\t\t.range-tab {\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666;\n\t\t\tpadding: 10rpx 0;\n\t\t}\n\n\t\t.range-tab-active {\n\t\t\tcolor: #007AFF;\n\t\t\tposition: relative;\n\t\t}\n\n\t\t/* picker-view 样式 */\n\t\t.picker-body {\n\t\t\tposition: relative;\n\t\t}\n\n\t\t.picker-view {\n\t\t\twidth: 100%;\n\t\t\theight: 264px;\n\t\t}\n\n\t\t.picker-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\theight: 44px;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.picker-text {\n\t\t\tfont-size: 16px;\n\t\t\tcolor: #333;\n\t\t}\n\n\n\t</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BAgGQ;AAXF;;kBAgJ<PERSON>,MAAO;YACN,IAAI,CAAC,QAAQ;QACd;;;;;;;;;0BArOW,KAAA,OAAO;YAAnB,IA0EO,QAAA,gBA1Ec,WAAM,kBAAkB,aAAO,KAAA,cAAc;gBACjE,IAwEO,QAAA,IAxED,WAAM,gBAAgB,aAAK,cAAN,KAAA,CAAA,GAAc;oBAAA;iBAAA;oBACxC,IAsEO,QAAA,IAtED,WAAM,8BAA2B;wBAEtC,IAMO,QAAA,IAND,WAAM,WAAQ;4BACnB,IAA4D,QAAA,IAAtD,WAAM,sBAAsB,aAAO,KAAA,QAAQ,GAAE,MAAE,CAAA,EAAA;gCAAA;6BAAA;4BACrD,IAAiD,QAAA,IAA3C,WAAM,cAAW,IAAI,KAAA,YAAY,GAAA,CAAA;4BACvC,IAEO,QAAA,IAFD,WAAM,0BAAuB;gCAClC,IAA8D,QAAA,IAAxD,WAAM,uBAAuB,aAAO,KAAA,SAAS,GAAE,MAAE,CAAA,EAAA;oCAAA;iCAAA;;;wBAK7C,IAAA,KAAA,YAAY,CAAC,MAAM,GAAA,CAAA;4BAA/B,IASO,QAAA,gBAT8B,WAAM;gCAC1C,IAOc,eAAA,IAPD,eAAU,cAAa,WAAM,wBAAuB,oBAAe;oCAC/E,IAKO,QAAA,IALD,WAAM,kBAAe;wCAC1B,IAGO,UAAA,IAAA,EAAA,cAAA,UAAA,CAHyB,KAAA,YAAY,EAAA,IAA9B,QAAQ,OAAR,SAAM,UAAA,GAAA,CAAA;mDAApB,IAGO,QAAA,IAHwC,SAAK,OAAO,WAAK,IAAA;gDAAC;gDACxD,IAAA,wBAAA,KAAA,iBAAA,IAAA;6CAAmD,GAAG,aAAK,KAAA;gDAAE,KAAA,oBAAoB,CAAC;4CAAK,QAC5F,OAAO,KAAK,GAAA,EAAA,EAAA;gDAAA;6CAAA;;;;;;;;mCAOP,KAAA,OAAO;4BAAnB,IAOO,QAAA,gBAPc,WAAM;gCAC1B,IAEO,QAAA,IAFD,WAAK,IAAA;oCAAC;oCAAoB,IAAA,uBAAA,KAAA,UAAA,IAAA,CAAA;iCAAuC,GAAG,aAAK,KAAA;oCAAE,KAAA,aAAa,CAAA,CAAA;gCAAA,IAAK,UAEnG,EAAA,EAAA;oCAAA;iCAAA;gCACA,IAEO,QAAA,IAFD,WAAK,IAAA;oCAAC;oCAAoB,IAAA,uBAAA,KAAA,UAAA,IAAA,CAAA;iCAAuC,GAAG,aAAK,KAAA;oCAAE,KAAA,aAAa,CAAA,CAAA;gCAAA,IAAK,UAEnG,EAAA,EAAA;oCAAA;iCAAA;;;;;wBAID,IAkCO,QAAA,IAlCD,WAAM,gBAAa;4BACxB,IAgCc,wBAAA,IAhCA,WAAO,KAAA,WAAW,EAAG,cAAQ,KAAA,cAAc,EAAE,WAAM,eAC/D,qBAAiB,KAAA,cAAc,EAAG,WAAK,IAAE,IAAA,aAAA,KAAA,KAAA,MAAA,GAAA,uCAC1C,gBAIqB,GAAA;uCAAA;+CAJK,KAAA,QAAQ;wCAAlC,IAIqB,+BAAA,IAAA,SAAA,CAAA,GAAA,6BAHM,gBAAqB,GAAA;mDAAA;gDAA/C,IAEO,UAAA,IAAA,EAAA,cAAA,UAAA,CAFkC,KAAA,KAAK,EAAA,IAAb,MAAA,OAAA,SAAI,UAAA,GAAA,CAAA;2DAArC,IAEO,QAAA,IAFD,WAAM,eAAqC,SAAK;wDACrD,IAA4C,QAAA,IAAtC,WAAM,gBAAa,IAAI,QAAO,KAAC,CAAA;;;;;;;;+CAGb,KAAA,SAAS;wCAAnC,IAIqB,+BAAA,IAAA,SAAA,CAAA,GAAA,6BAHM,gBAAuB,GAAA;mDAAA;gDAAjD,IAEO,UAAA,IAAA,EAAA,cAAA,UAAA,CAFmC,KAAA,MAAM,EAAA,IAAf,OAAA,OAAA,SAAK,UAAA,GAAA,CAAA;2DAAtC,IAEO,QAAA,IAFD,WAAM,eAAuC,SAAK;wDACvD,IAA6C,QAAA,IAAvC,WAAM,gBAAa,IAAI,SAAQ,KAAC,CAAA;;;;;;;;+CAGd,KAAA,OAAO;wCAAjC,IAIqB,+BAAA,IAAA,SAAA,CAAA,GAAA,6BAHM,gBAAmB,GAAA;mDAAA;gDAA7C,IAEO,UAAA,IAAA,EAAA,cAAA,UAAA,CAFiC,KAAA,IAAI,EAAA,IAAX,KAAA,OAAA,SAAG,UAAA,GAAA,CAAA;2DAApC,IAEO,QAAA,IAFD,WAAM,eAAmC,SAAK;wDACnD,IAA2C,QAAA,IAArC,WAAM,gBAAa,IAAI,OAAM,KAAC,CAAA;;;;;;;;+CAGZ,KAAA,QAAQ;wCAAlC,IAIqB,+BAAA,IAAA,SAAA,CAAA,GAAA,6BAHM,gBAAqB,GAAA;mDAAA;gDAA/C,IAEO,UAAA,IAAA,EAAA,cAAA,UAAA,CAFkC,KAAA,KAAK,EAAA,IAAb,MAAA,OAAA,SAAI,UAAA,GAAA,CAAA;2DAArC,IAEO,QAAA,IAFD,WAAM,eAAqC,SAAK;wDACrD,IAA4C,QAAA,IAAtC,WAAM,gBAAa,IAAI,QAAO,KAAC,CAAA;;;;;;;;+CAGb,KAAA,UAAU;wCAApC,IAIqB,+BAAA,IAAA,SAAA,CAAA,GAAA,6BAHM,gBAAyB,GAAA;mDAAA;gDAAnD,IAEO,UAAA,IAAA,EAAA,cAAA,UAAA,CAFoC,KAAA,OAAO,EAAA,IAAjB,QAAA,OAAA,SAAM,UAAA,GAAA,CAAA;2DAAvC,IAEO,QAAA,IAFD,WAAM,eAAyC,SAAK;wDACzD,IAA8C,QAAA,IAAxC,WAAM,gBAAa,IAAI,UAAS,KAAC,CAAA;;;;;;;;+CAGf,KAAA,UAAU;wCAApC,IAIqB,+BAAA,IAAA,SAAA,CAAA,GAAA,6BAHM,gBAAyB,GAAA;mDAAA;gDAAnD,IAEO,UAAA,IAAA,EAAA,cAAA,UAAA,CAFoC,KAAA,OAAO,EAAA,IAAjB,QAAA,OAAA,SAAM,UAAA,GAAA,CAAA;2DAAvC,IAEO,QAAA,IAFD,WAAM,eAAyC,SAAK;wDACzD,IAA8C,QAAA,IAAxC,WAAM,gBAAa,IAAI,UAAS,KAAC,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;aA8D3C,SAAkB,OAAO;aAEzB,aAAoB;aAEpB,sBAA2B;aAE3B,YAAiB,MAAM;aAEvB,sBAAmB,MAAM;aAEzB,mBAAyB,MAAM;aAE/B,gBAAa,MAAM;aAEnB,iBAAc,MAAM;aAEpB,eAAY,MAAM;aAElB,gBAAa,MAAM;aAEnB,kBAAe,MAAM;aAErB,kBAAe,MAAM;aAErB,eAAwB,OAAM;sBAKpB,OAAM;uBAIL,OAAM;wBAKL,OAAM;sBAKR,OAAM;uBAKL,OAAM;yBAKJ,OAAM;yBAKN,OAAM;6BAMF,MAAK;2BAIP,MAAK;kCAsBE,MAAK;;;QA7F5B,IAAM,KAAK,OAAO,AAAI;mBAGrB,aAAS,KAAI,CAAA,EAAA,CAAK,OAAO,EAEzB,iBAAa,IAAE,EAAA,CAAK,MAEpB,iBAAa,IAAc,MAAb,KAAK,MAEnB,gBAAY,CAAA,CAAA,EAAA,CAAK,MAAM,EAEvB,iBAAa,IAAM,MAAM,KAEzB,uBAAmB,CAAC,CAAA,CAAA,EAAA,CAAK,MAAM,EAE/B,WAAO,IAAM,MAAM,KAEnB,YAAQ,IAAM,MAAM,KAEpB,UAAM,IAAM,MAAM,KAElB,WAAO,IAAM,MAAM,KAEnB,aAAS,IAAM,MAAM,KAErB,aAAS,IAAM,MAAM,KAErB,mBAAe,KAAI,CAAA,EAAA,CAAK,OAAM,wBAKpB,OAAM,EAAjB,OAAW,OAAM,CAAA;YAChB,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC3B;kCAEY,OAAM,EAAlB,OAAY,OAAM,CAAA;YACjB,OAAO;gBAAC;gBAAY;gBAAQ;gBAAQ;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAChE;gBAAC;gBAAkB;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxD;mCAEa,OAAM,EAAnB,OAAa,OAAM,CAAA;YAClB,OAAO;gBAAC;gBAAY;gBAAQ;gBAAc;aAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KACjE;gBAAC;gBAAkB;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxD;iCAEW,OAAM,EAAjB,OAAW,OAAM,CAAA;YAChB,OAAO;gBAAC;gBAAY;gBAAQ;aAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KACjD;gBAAC;gBAAkB;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxD;kCAEY,OAAM,EAAlB,OAAY,OAAM,CAAA;YACjB,OAAO;gBAAC;gBAAY;gBAAQ;gBAAe;aAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAC/E;gBAAC;gBAAkB;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxD;oCAEc,OAAM,EAApB,OAAc,OAAM,CAAA;YACnB,OAAO;gBAAC;gBAAY;gBAAQ;gBAAe;aAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAC/E;gBAAC;gBAAkB;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxD;oCAEc,OAAM,EAApB,OAAc,OAAM,CAAA;YACnB,OAAO;gBAAC;aAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,KAC5C,CAAC,IAAI,CAAC,WAAU,IAAK;gBAAC;gBAAY;aAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,KAC7D,CAAC,IAAI,CAAC,WAAU,IAAK;gBAAC;gBAAkB;aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC;QAC9E;wCAEkB,MAAK,EAAvB,OAAkB,MAAK,CAAA;YACtB,OAAO;QACR;sCAEgB,MAAK,EAArB,OAAgB,MAAK,CAAA;YACpB,IAAI,IAAI,CAAC,KAAI,IAAK;gBAAQ,OAAO,IAAI,CAAC,KAAI;;YAE1C,IAAM,SAAS,gBAAgB,uBAAA,qBAAA,WAAA,wDAAA,GAAA,EAAA,EAAA;gBAC9B,eAAY;gBACZ,WAAQ;gBACR,WAAQ;gBACR,WAAQ;gBACR,mBAAc;gBACd,YAAS;gBACT,UAAO;gBACP,oBAAe;gBACf,2BAAsB;gBACtB,uBAAkB;gBAClB,mBAAc;gBACd,mBAAc;aACf;YAEA,IAAM,SAAS,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA,CAAA,EAAA,CAAK,MAAK;YAC1C,OAAO,IAAA,UAAU,IAAG;gBAAI;;gBAAS;;QAClC;6CAEuB,MAAK,EAA5B,OAAuB,MAAK,CAAA;YAC3B,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAM,gBAAgB,MAAK,GAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAE,EAAA,CAAK;gBACvG,IAAM,cAAc,MAAK,GAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAE,EAAA,CAAK;gBACrG,OAAO,iBAAiB,QAAQ;mBAC1B;gBACN,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI;;QAEpD;;;aAOA;aAAA,kBAAW,MAAM,KAAW,EAAE,MAAM,YAAY,GAAG,MAAK,CAAA;QACvD,IAAI,QAAQ,IAAI;YAAE,OAAO;;QAEzB,IAAI;YAEH,IAAM,GAAG,OAAO;YAChB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;gBAAI,OAAO;;YAElC,IAAM,MAAM,MAAK,GAAI,EAAE,WAAW;YAClC,IAAM,OAAO,MAAK,GAAI,CAAC,EAAE,QAAQ,KAAK,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YAChE,IAAM,KAAK,MAAK,GAAI,EAAE,OAAO,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YACvD,IAAM,MAAM,MAAK,GAAI,EAAE,QAAQ,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YACzD,IAAM,QAAQ,MAAK,GAAI,EAAE,UAAU,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YAC7D,IAAM,QAAQ,MAAK,GAAI,EAAE,UAAU,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YAE7D,MAAQ;gBACF;oBACJ,OAAO,KAAG,OAAI,MAAI,QAAK,MAAI,MAAG,MAAI,OAAI,MAAI,SAAS,CAAA,IAAA,IAAI,CAAC,WAAU;wBAAI,MAAM;;wBAAS;;oBAAA;gBACjF;oBACJ,OAAO,KAAG,OAAI,MAAI,QAAK,MAAI;gBACvB;oBACJ,OAAO,KAAG,OAAI,MAAI,SAAS,CAAA,IAAA,IAAI,CAAC,WAAU;wBAAI,MAAM;;wBAAS;;oBAAA;gBACzD;oBACJ,OAAO,KAAG;gBACN;oBACJ,OAAO,KAAG,OAAI,MAAI;gBACd;oBACJ,OAAO,KAAG;gBACN;oBACJ,OAAO,KAAG;gBACN;oBACJ,OAAO,KAAG,OAAI,MAAI;gBACd;oBACJ,OAAO,KAAG,OAAI,MAAI,SAAM,MAAI;gBAC7B;oBACC,OAAO,KAAG,OAAI,MAAI,QAAK,MAAI,MAAG,MAAI,OAAI,MAAI,SAAS,CAAA,IAAA,IAAI,CAAC,WAAU;wBAAI,MAAM;;wBAAS;;oBAAA;;;SAEtF,OAAO,kBAAO;YACf,QAAQ,KAAK,CAAC,sBAAsB,OAAO,MAAI;YAC/C,OAAO;;IAET;aAGA,UAAU,WAAoC,GAAG,KAAG;QACnD,IAAI,SAAS,IAAI;YAAE,OAAO,AAAI;;QAE9B,IAAI;YACH,IAAI,MAAM,QAAc,IAAG;YAC3B,IAAI,SAAiB,MAAM;gBAE1B,OAAO,AAAI,KAAK,CAAA,MAAK,EAAA,CAAA,IAAA,EAAC,OAAO;mBACvB,IAAI,oBAAO,UAAS,YAAY,CAAC,MAAM,MAAK,EAAA,CAAA,MAAA,GAAG;gBAErD,OAAO,AAAI,KAAK,MAAI,EAAA,CAAK,MAAM;mBACzB,IAAI,oBAAO,UAAS,UAAU;gBAEpC,IAAI,CAAA,MAAK,EAAA,CAAA,MAAA,EAAC,QAAQ,CAAC,MAAM;oBAExB,OAAO,AAAI,KAAK,MAAI,EAAA,CAAK,MAAM;uBACzB,IAAI,CAAA,MAAK,EAAA,CAAA,MAAA,EAAC,QAAQ,CAAC,QAAQ,CAAA,MAAK,EAAA,CAAA,MAAA,EAAC,QAAQ,CAAC,MAAM;oBAEtD,IAAM,gBAAO,MAAM,IAAK,CAAA,MAAK,EAAA,CAAA,MAAA,EAAC,KAAK,CAAC,2BAAW,GAAG,CAAC,IAAA,IAAA,MAAA;+BAAK,SAAS;;oBACjE,IAAI,MAAM,MAAK,IAAK,CAAC,EAAE;wBACtB,OAAO,AAAI,KACV,KAAK,CAAC,CAAC,CAAC,EACR,KAAK,CAAC,CAAC,CAAA,GAAI,CAAC,EACZ,KAAK,CAAC,CAAC,CAAC,EACR,IAAA,MAAM,MAAK,GAAI,CAAA;4BAAI,KAAK,CAAC,CAAC,CAAA;;AAAI,6BAAC;;wBAAA,EAC/B,IAAA,MAAM,MAAK,GAAI,CAAA;4BAAI,KAAK,CAAC,CAAC,CAAA;;AAAI,6BAAC;;wBAAA,EAC/B,IAAA,MAAM,MAAK,GAAI,CAAA;4BAAI,KAAK,CAAC,CAAC,CAAA;;AAAI,6BAAA;;wBAAA;;uBAG1B;oBAEN,IAAM,WAAW,MAAK,GAAI,SAAS,MAAK,EAAA,CAAA,MAAA;oBACxC,IAAI,CAAC,MAAM,YAAY;wBACtB,OAAO,AAAI,KAAK,UAAQ,EAAA,CAAK,MAAM;;;;YAKtC,OAAO,IAAA,QAAQ,IAAG,IAAK,CAAC,MAAM,KAAK,OAAO;gBAAM;;gBAAW;;;SAC1D,OAAO,kBAAO;YACf,QAAQ,KAAK,CAAC,qBAAqB,OAAK;YACxC,OAAO,AAAI;;IAEb;aAGA;aAAA,oBAAa,MAAM,IAAI,GAAG,OAAM,CAAA;QAC/B,IAAI,MAAM,KAAK,OAAO,KAAK;YAC1B,QAAQ,IAAI,CAAC,iBAAiB,MAAI;YAClC,OAAO,KAAI;;QAGZ,IAAM,MAAM,MAAK,GAAI,KAAK,WAAW;QACrC,IAAI,OAAO,IAAI,CAAC,SAAQ,IAAK,OAAO,IAAI,CAAC,OAAO,EAAE;YACjD,QAAQ,IAAI,CAAC,sBAAsB,MAAI;YACvC,OAAO,KAAI;;QAGZ,OAAO,IAAG;IACX;aAGA,KAAK,WAAuC,EAAA;QAC3C,IAAI,CAAC,OAAM,GAAI,IAAG;QAClB,IAAI,CAAC,iBAAgB,GAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,UAAS,GAAI,CAAA;QAElB,IAAI;YACH,IAAI,IAAI,CAAC,OAAO,EAAE;gBAEjB,IAAI,SAAM,OAAO,CAAC,UAAU,CAAA,MAAK,EAAA,UAAA,KAAA,EAAC,MAAK,IAAK,CAAC,EAAE;oBAC9C,IAAI,CAAC,WAAU,GAAI,CAAA,MAAK,EAAA,UAAA,KAAA,EAAC,GAAG,CAAC,IAAA,IAAA;+BAAK,IAAI,CAAC,SAAS,CAAC;;uBAC3C,IAAI,oBAAO,UAAS,UAAU;oBAEpC,IAAM,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,MAAK,EAAA,CAAA,MAAA;oBACvC,IAAI,CAAC,WAAU,GAAI;wBAAC;wBAAM;qBAAI;uBACxB;oBACN,IAAM,KAAK,OAAO,AAAI;oBACtB,IAAI,CAAC,WAAU,GAAI;wBAAC;wBAAK;qBAAG;iBAC7B;mBACM;gBAEN,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,SAAS,CAAC;;YAGnC,IAAI,CAAC,WAAS,CAAC,KAAI;gBAClB,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,kBAAkB;YACxB;;;SACC,OAAO,kBAAO;YACf,QAAQ,KAAK,CAAC,sBAAsB,OAAK;YACzC,IAAM,KAAK,OAAO,AAAI;YACtB,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,WAAU,GAAI;oBAAC;oBAAK;iBAAG;mBACtB;gBACN,IAAI,CAAC,WAAU,GAAI;;;IAGtB;aAGA;aAAA,cAAI;QACH,IAAI,CAAC,OAAM,GAAI,KAAI;IACpB;aAGA;aAAA,wBAAc;QACb,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,OAAK,CAAC;IACZ;aAGA;aAAA,kBAAQ;QACP,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,OAAK,CAAC;IACZ;aAGA;aAAA,sBAAe,MAAM,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG,EAAA;QAC1C,IAAM,MAAM,AAAI,IAAI,MAAM,EAAE,GAAG;QAE/B,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW;;QAEjC,IAAI,IAAI,CAAC,SAAS,EAAE;YACnB,IAAI,GAAG,CAAC,SAAS,KAAK,QAAQ,KAAK,CAAC;;QAErC,IAAI,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,GAAG,CAAC,OAAO,KAAK,OAAO;;QAE5B,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ;;QAE9B,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,GAAG,CAAC,UAAU,KAAK,UAAU;;QAElC,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,GAAG,CAAC,UAAU,KAAK,UAAU;;QAGlC,OAAO;IACR;aAGA;aAAA,mBAAS;QACR,IAAI;YACH,IAAI,IAAI,CAAC,OAAO,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG;oBACvF,+BACC,QAAO,UACP,OAAM;oBAEP;;gBAGD,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA,GAAI,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE;oBAC9C,+BACC,QAAO,gBACP,OAAM;oBAEP;;gBAGD,IAAM,qBAAY,QAAS,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAA,OAAG;2BAAK,AAAI,KAAK,KAAK,OAAO;;gBAC7E,IAAM,oBAAW,IAAI,MAAM,EAAE,GAAG,KAAM,WAAW,GAAG,CAAC,IAAA,OAAG,IAAA,MAAA,EAAA,GAAA;2BAAK,IAAI,CAAC,cAAc,CAAC;;gBACjF,IAAM,WAAW,MAAK,GAAI,WAAW,GAAG,CAAC,IAAA,OAAG,MAAA;2BAAK,IAAI,CAAC,UAAU,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,IAAE,EAAA,CAAK;mBAAe,IAAI,CAAC;gBAE9H,IAAI,CAAC,OAAK,CAAC,WAAW,IACrB,WAAO,WACP,eAAA;mBAEK;gBACN,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,GAAG;oBACzC,+BACC,QAAO,UACP,OAAM;oBAEP;;gBAGD,IAAM,WAAW,OAAO,AAAI,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO;gBACzD,IAAM,UAAU,IAAI,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC;gBACvD,IAAM,WAAW,MAAK,GAAI,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI;gBAE9D,IAAI,CAAC,OAAK,CAAC,WAAW,IACrB,WAAO,UACP,eAAA;;YAIF,IAAI,CAAC,IAAI;;SACR,OAAO,kBAAO;YACf,QAAQ,KAAK,CAAC,kBAAkB,OAAK;YACrC,+BACC,QAAO,QACP,OAAM;;IAGT;aAGA;aAAA,4BAAqB,OAAO,MAAM,EAAA;QACjC,IAAI,QAAQ,CAAA,IAAK,SAAS,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE;YACnD,QAAQ,IAAI,CAAC,+BAA+B,OAAK;YACjD;;QAGD,IAAM,0BAA0B,IAAI,CAAC,YAAY,CAAC,MAAK;QACvD,IAAI,CAAC,aAAa,CAAC,QAAQ;IAC5B;aAGA;aAAA,qBAAc,uBAAuB,EAAE,OAAO,MAAM,EAAA;QACnD,IAAI,CAAC,iBAAgB,GAAI;QACzB,IAAI,CAAC,UAAS,GAAI,CAAA;QAElB,IAAI;YACH,IAAI,IAAI,CAAC,OAAO,EAAE;gBAEjB,IAAI,SAAM,OAAO,CAAC,OAAO,KAAK,GAAG;oBAEhC,IAAM,qBAAY,QAAS,OAAO,KAAI,CAAA,EAAA,UAAK;oBAC3C,IAAI,WAAW,MAAK,IAAK,CAAC,EAAE;wBAC3B,QAAQ,IAAI,CAAC,0DAA0D,QAAM;wBAC7E;;oBAED,IAAI,CAAC,WAAU,GAAI,WAAW,GAAG,CAAC,IAAA,IAAA;+BAAK,IAAI,CAAC,SAAS,CAAC;;uBAChD;oBAEN,IAAM,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO,KAAI,CAAA,EAAA,CAAK;oBAClD,IAAI,CAAC,WAAU,GAAI;wBAAC;wBAAM;qBAAI;iBAC/B;mBACM;gBAEN,IAAI,SAAM,OAAO,CAAC,OAAO,KAAK,GAAG;oBAEhC,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,KAAI,CAAA,EAAA,UAAK,KAAM,CAAC,CAAC,CAAC,CAAC;uBACvD;oBAEN,IAAI,CAAC,WAAU,GAAI,IAAI,CAAC,SAAS,CAAC,OAAO,KAAI,CAAA,EAAA,CAAK;;;YAIpD,IAAI,CAAC,WAAS,CAAC,KAAI;gBAClB,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,kBAAkB;YACxB;;YAEA,IAAI,OAAO,WAAU,IAAK,IAAI,EAAE;gBAC/B,IAAI,CAAC,SAAS;;;SAEd,OAAO,kBAAO;YACf,QAAQ,KAAK,CAAC,uBAAuB,OAAK;YAC1C,+BACC,QAAO,YACP,OAAM;;IAGT;aAGA;aAAA,sBAAe,GAAG,wBAAwB,EAAA;QACzC,IAAI,CAAC,WAAU,GAAI,EAAE,MAAM,CAAC,KAAI;QAChC,IAAI,CAAC,iBAAgB,GAAI,CAAC,CAAA;QAC1B,IAAI,CAAC,mBAAmB;IACzB;aAGA;aAAA,qBAAc,OAAO,MAAM,EAAA;QAC1B,IAAI,IAAI,CAAC,UAAS,IAAK;YAAO;;QAC9B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,WAAS,CAAC,KAAI;YAClB,IAAI,CAAC,kBAAkB;QACxB;;IACD;aAGA;aAAA,kBAAQ;QACP,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YAExB,IAAI,CAAC,KAAI,GAAI,KAAC;gBACd;gBAAK,IAAI,GAAG,MAAK,GAAI,IAAI,CAAC,SAAS;gBAAnC,MAAqC,KAAK,IAAI,CAAC,OAAO;oBACrD,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA;oBAD6B;;;YAKxD,IAAI,CAAC,MAAK,GAAI,KAAC;gBACf;gBAAK,IAAI,GAAG,MAAK,GAAI,CAAC;gBAAtB,MAAwB,KAAK,EAAE;oBAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;oBADV;;;YAKjC,IAAI,CAAC,KAAI,GAAI,KAAC;gBACd;gBAAK,IAAI,GAAG,MAAK,GAAI,CAAC;gBAAtB,MAAwB,KAAK,EAAE;oBAC9B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;oBADT;;;YAKjC,IAAI,CAAC,OAAM,GAAI,KAAC;gBAChB;gBAAK,IAAI,GAAG,MAAK,GAAI,CAAC;gBAAtB,MAAwB,KAAK,EAAE;oBAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;oBADX;;;YAKjC,IAAI,CAAC,OAAM,GAAI,KAAC;gBAChB;gBAAK,IAAI,GAAG,MAAK,GAAI,CAAC;gBAAtB,MAAwB,KAAK,EAAE;oBAC9B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;oBADX;;;YAIjC,IAAI,CAAC,aAAY,GAAI,IAAG;;QAIzB,IAAI,CAAC,cAAc;IACpB;aAGA;aAAA,wBAAc;QACb,IAAM,MAAM,OAAO,IAAA,IAAI,CAAC,OAAM;YAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAA;;YAAI,IAAI,CAAC,WAAU;;QACrF,IAAM,MAAM,MAAK,GAAI,KAAK,WAAW;QACrC,IAAM,OAAO,MAAK,GAAI,KAAK,QAAQ,KAAK,CAAA;QAGxC,IAAM,aAAa,MAAK,GAAI,IAAI,CAAC,cAAc,CAAC,MAAM;QAEtD,IAAI,CAAC,IAAG,GAAI,KAAC;YACb;YAAK,IAAI,GAAG,MAAK,GAAI,CAAC;YAAtB,MAAwB,KAAK;gBAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;gBADC;;;IAG3C;aAGA;aAAA,sBAAe,MAAM,MAAM,EAAE,OAAO,MAAM,GAAG,MAAK,CAAA;QAEjD,OAAO,AAAI,KAAK,MAAM,OAAO,CAAC,EAAE,OAAO;IACxC;aAGA;aAAA,4BAAkB;QACjB,IAAM,MAAM,OAAO,IAAA,IAAI,CAAC,OAAM;YAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAA;;YAAI,IAAI,CAAC,WAAU;;QACrF,IAAI,MAAM,KAAK,OAAO,KAAK;YAC1B,QAAQ,IAAI,CAAC,uCAAuC,MAAI;YACxD;;QAID,IAAI,CAAC,cAAc;QAEnB,IAAM,iBAAQ,MAAM,IAAK,KAAC;QAE1B,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAM,WAAW,MAAK,GAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAA,IAAA,OAAA;uBAAK,SAAS,MAAM,KAAK,WAAW;;;YACnF,OAAO,IAAI,CAAC,IAAA,aAAa,CAAA;gBAAI;;AAAY,iBAAC;;YAAA;;QAG3C,IAAI,IAAI,CAAC,SAAS,EAAE;YACnB,IAAM,UAAU,MAAK,GAAI,CAAC,KAAK,QAAQ,KAAK,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YACtE,IAAM,YAAY,MAAK,GAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAA,IAAA,OAAA;uBAAK,KAAK;;;YAC3D,OAAO,IAAI,CAAC,IAAA,cAAc,CAAA;gBAAI;;AAAa,iBAAC;;YAAA;;QAG7C,IAAI,IAAI,CAAC,OAAO,EAAE;YACjB,IAAM,QAAQ,MAAK,GAAI,KAAK,OAAO,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YAC7D,IAAM,UAAU,MAAK,GAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAA,IAAA,OAAA;uBAAK,KAAK;;;YAEvD,IAAI,YAAY,CAAA,IAAK,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACjD,OAAO,IAAI,CAAC;mBACN;gBAEN,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,MAAK,GAAI,CAAC;;;QAIlC,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClB,IAAM,SAAS,MAAK,GAAI,KAAK,QAAQ,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YAC/D,IAAM,WAAW,MAAK,GAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,IAAA,IAAA,OAAA;uBAAK,KAAK;;;YACzD,OAAO,IAAI,CAAC,IAAA,aAAa,CAAA;gBAAI;;AAAY,iBAAC;;YAAA;;QAG3C,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,IAAM,WAAW,MAAK,GAAI,KAAK,UAAU,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YACnE,IAAM,aAAa,MAAK,GAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAA,IAAA,OAAA;uBAAK,KAAK;;;YAC7D,OAAO,IAAI,CAAC,IAAA,eAAe,CAAA;gBAAI;;AAAc,iBAAC;;YAAA;;QAG/C,IAAI,IAAI,CAAC,UAAU,EAAE;YACpB,IAAM,WAAW,MAAK,GAAI,KAAK,UAAU,GAAG,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;YACnE,IAAM,aAAa,MAAK,GAAI,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,IAAA,IAAA,OAAA;uBAAK,KAAK;;;YAC7D,OAAO,IAAI,CAAC,IAAA,eAAe,CAAA;gBAAI;;AAAc,iBAAC;;YAAA;;QAG/C,IAAI,CAAC,WAAU,GAAQ;IACxB;aAGA;aAAA,6BAAmB;QAClB,IAAI,CAAC,SAAM,OAAO,CAAC,IAAI,CAAC,WAAW;YAAG;;QAEtC,IAAI,OAAO,MAAK,GAAI,CAAA;QACpB,IAAI,MAAM,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,WAAW;QAC/C,IAAI,OAAO,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;QAC7C,IAAI,KAAK,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,OAAO;QAC1C,IAAI,MAAM,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;QAC5C,IAAI,QAAQ,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,UAAU;QAChD,IAAI,QAAQ,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,UAAU;QAGhD,IAAM,cAAc,MAAK,GAAI;QAC7B,IAAM,eAAe,MAAK,GAAI;QAE9B,IAAI,IAAI,CAAC,QAAO,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACrD,OAAO,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACnD;;QAGD,IAAI,IAAI,CAAC,SAAQ,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACtD,QAAQ,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAA;YACzD;;QAID,IAAM,kBAAkB,OAAM,GAAI,CAAC,QAAQ,gBAAgB,SAAS,aAAa;QAEjF,IAAI,kBAAkB;YAErB,IAAM,gBAAgB,MAAK,GAAI,IAAI,CAAC,cAAc,CAAC,MAAM,QAAQ,CAAC;YAGlE,IAAI,MAAM,gBAAgB;gBACzB,MAAM;;YAIP,IAAI,CAAC,IAAG,GAAI,KAAC;gBACb;gBAAK,IAAI,GAAG,MAAK,GAAI,CAAC;gBAAtB,MAAwB,KAAK;oBAC5B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAA,EAAA,EAAG,QAAQ,CAAC,CAAC,EAAE;oBADI;;;YAK7C,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACpD,IAAM,iBAAiB,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,MAAK;gBACtD,IAAI,mBAAmB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBAExC,IAAI,CAAC,WAAW,CAAC,MAAK,GAAI,IAAI,CAAC,IAAI,CAAC,MAAK,GAAI,CAAA;;;;QAKhD,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACpD,IAAM,UAAU,MAAK,GAAI,IAAI,CAAC,WAAW,CAAC,MAAK;YAE/C,IAAI,YAAY,CAAA,IAAK,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACjD,IAAM,aAAa,MAAK,GAAI,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS;gBACxD,MAAM;mBACA;gBAEN,MAAM,IAAI,CAAC,IAAI,CAAC,MAAK;;YAEtB;;QAGD,IAAI,IAAI,CAAC,QAAO,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACrD,OAAO,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACnD;;QAGD,IAAI,IAAI,CAAC,UAAS,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACvD,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;YACvD;;QAGD,IAAI,IAAI,CAAC,UAAS,IAAK,QAAQ,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YACvD,SAAS,SAAS,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;;QAGxD,IAAM,SAAS,OAAO,AAAI,KAAK,MAAM,OAAO,KAAK,MAAM,QAAQ;QAE/D,IAAI,IAAI,CAAC,OAAO,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAA,GAAI;YACpC,IAAI,IAAI,CAAC,UAAS,IAAK,CAAA,IAAK,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA,GAAI,SAAS;gBAC1D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA,GAAI,AAAI,KAAK,QAAQ,OAAO;;eAEzC;YACN,IAAI,CAAC,WAAU,GAAI;;QAIpB,IAAI,CAAC,kBAAkB;YACtB,IAAI,CAAC,cAAc;;IAErB;;mBA1qBK;;;;;;;;;;;;;2EAMK,WAAS,EAAA,CAAK,+DAKd,gEAKA,KAAI,uDAKJ,OAAA,MAAA;mBAAK,IAAG;;6DAKR,OAAA,MAAA;mBAAM,IAAG;;iEAKT;mBAAM;;4DAKN,GAAE;;;;;;;;;;;;AAwoBd"}