{"version": 3, "sources": ["../../../../../../../../Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/socket.ts", "App.uvue", "../../../../../../../../Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts", "components/main-form/form_type.uts", "components/main-form/tools/main-datetime-picker.uvue", "components/main-color-picker/main-color-picker.uvue", "components/main-form/tools/main-color-picker.uvue", "components/main-form/components/form-select.uvue", "main.uts"], "sourcesContent": ["/// <reference types=\"@dcloudio/uni-app-x/types/uni/global\" />\n// 之所以又写了一份，是因为外层的socket，connectSocket的时候必须传入multiple:true\n// 但是android又不能传入，目前代码里又不能写条件编译之类的。\nexport function initRuntimeSocket(\n  hosts: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(null)\n  return hosts\n    .split(',')\n    .reduce<Promise<SocketTask | null>>(\n      (\n        promise: Promise<SocketTask | null>,\n        host: string\n      ): Promise<SocketTask | null> => {\n        return promise.then((socket): Promise<SocketTask | null> => {\n          if (socket != null) return Promise.resolve(socket)\n          return tryConnectSocket(host, port, id)\n        })\n      },\n      Promise.resolve(null)\n    )\n}\n\nconst SOCKET_TIMEOUT = 500\nfunction tryConnectSocket(\n  host: string,\n  port: string,\n  id: string\n): Promise<SocketTask | null> {\n  return new Promise((resolve, reject) => {\n    const socket = uni.connectSocket({\n      url: `ws://${host}:${port}/${id}`,\n      fail() {\n        resolve(null)\n      },\n    })\n    const timer = setTimeout(() => {\n      // @ts-expect-error\n      socket.close({\n        code: 1006,\n        reason: 'connect timeout',\n      } as CloseSocketOptions)\n      resolve(null)\n    }, SOCKET_TIMEOUT)\n\n    socket.onOpen((e) => {\n      clearTimeout(timer)\n      resolve(socket)\n    })\n    socket.onClose((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n    socket.onError((e) => {\n      clearTimeout(timer)\n      resolve(null)\n    })\n  })\n}\n", "<script lang=\"uts\">\r\n\r\n\tlet firstBackTime = 0\r\n\r\n\texport default {\r\n\t\tonLaunch: function () {\r\n\t\t\tconsole.log('App Launch')\r\n\t\t},\r\n\t\tonShow: function () {\r\n\t\t\tconsole.log('App Show')\r\n\t\t},\r\n\t\tonHide: function () {\r\n\t\t\tconsole.log('App Hide')\r\n\t\t},\r\n\r\n\t\tonLastPageBackPress: function () {\r\n\t\t\tconsole.log('App LastPageBackPress')\r\n\t\t\tif (firstBackTime == 0) {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '再按一次退出应用',\r\n\t\t\t\t\tposition: 'bottom',\r\n\t\t\t\t})\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tfirstBackTime = 0\r\n\t\t\t\t}, 2000)\r\n\t\t\t} else if (Date.now() - firstBackTime < 2000) {\r\n\t\t\t\tfirstBackTime = Date.now()\r\n\t\t\t\tuni.exit()\r\n\t\t\t}\r\n\t\t},\r\n\r\n\t\tonExit: function () {\r\n\t\t\tconsole.log('App Exit')\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\t.uni-row {\r\n\t\tflex-direction: row;\r\n\t}\r\n\r\n\t.uni-column {\r\n\t\tflex-direction: column;\r\n\t}\r\n</style>", "import { initRuntimeSocket } from './socket'\n\nexport function initRuntimeSocketService(): Promise<boolean> {\n  const hosts: string = process.env.UNI_SOCKET_HOSTS\n  const port: string = process.env.UNI_SOCKET_PORT\n  const id: string = process.env.UNI_SOCKET_ID\n  if (hosts == '' || port == '' || id == '') return Promise.resolve(false)\n  let socketTask: SocketTask | null = null\n  __registerWebViewUniConsole(\n    (): string => {\n      return process.env.UNI_CONSOLE_WEBVIEW_EVAL_JS_CODE\n    },\n    (data: string) => {\n      socketTask?.send({\n        data,\n      } as SendSocketMessageOptions)\n    }\n  )\n  return Promise.resolve()\n    .then((): Promise<boolean> => {\n      return initRuntimeSocket(hosts, port, id).then((socket): boolean => {\n        if (socket == null) {\n          return false\n        }\n        socketTask = socket\n        return true\n      })\n    })\n    .catch((): boolean => {\n      return false\n    })\n}\n\ninitRuntimeSocketService()\n", "export type FormFieldData = {\r\n\tkey : string;\r\n\tname : string;\r\n\ttype : string;\r\n\tvalue : any;\r\n\tisSave ?: boolean;\r\n\tcondition ?: string;\r\n\textra : UTSJSONObject\r\n}\r\n\r\nexport type FormChangeEvent = {\r\n\tindex : number;\r\n\tvalue : any\r\n}\r\n\r\n\r\n\r\nexport type DateQuickOption = {\r\n\t\tlabel: string,\r\n\t\tvalue: Date | Date[],\r\n\t\tautoConfirm?: boolean\r\n\t}", "<template>\n\t<!-- 弹窗遮罩层 -->\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\n\t\t\t<view class=\"datetime-picker-container\">\n\t\t\t\t<!-- 导航栏 -->\n\t\t\t\t<view class=\"navbar\">\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\n\t\t\t\t\t<text class=\"nav-title\">{{ displayTitle }}</text>\n\t\t\t\t\t<view class=\"confirm-btn-container\">\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 快捷选项 -->\n\t\t\t\t<view v-if=\"quickOptions.length > 0\" class=\"quick-options-container\">\n\t\t\t\t\t<scroll-view direction=\"horizontal\" class=\"quick-options-scroll\" show-scrollbar=\"false\">\n\t\t\t\t\t\t<view class=\"quick-options\">\n\t\t\t\t\t\t\t<text v-for=\"(option, index) in quickOptions\" :key=\"index\" class=\"quick-item\"\n\t\t\t\t\t\t\t\t:class=\"{ 'quick-item-active': currentQuickIndex == index }\" @click=\"onQuickSelectByIndex(index)\">\n\t\t\t\t\t\t\t\t{{ option.label }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</scroll-view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 区间选择标签 -->\n\t\t\t\t<view v-if=\"isRange\" class=\"range-tabs\">\n\t\t\t\t\t<text class=\"range-tab\" :class=\"{ 'range-tab-active': rangeIndex == 0 }\" @click=\"onRangeChange(0)\">\n\t\t\t\t\t\t开始时间\n\t\t\t\t\t</text>\n\t\t\t\t\t<text class=\"range-tab\" :class=\"{ 'range-tab-active': rangeIndex == 1 }\" @click=\"onRangeChange(1)\">\n\t\t\t\t\t\t结束时间\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- picker-view 选择器 -->\n\t\t\t\t<view class=\"picker-body\">\n\t\t\t\t\t<picker-view :value=\"pickerValue\" @change=\"onPickerChange\" class=\"picker-view\"\n\t\t\t\t\t\t:indicator-style=\"indicatorStyle\" :style=\"{ height: `${height}px` }\">\n\t\t\t\t\t\t<picker-view-column v-if=\"showYear\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"year in years\" :key=\"year\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ year }}年</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showMonth\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"month in months\" :key=\"month\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ month }}月</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showDay\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"day in days\" :key=\"day\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ day }}日</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showHour\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"hour in hours\" :key=\"hour\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ hour }}时</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showMinute\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"minute in minutes\" :key=\"minute\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ minute }}分</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t\t<picker-view-column v-if=\"showSecond\">\n\t\t\t\t\t\t\t<view class=\"picker-item\" v-for=\"second in seconds\" :key=\"second\">\n\t\t\t\t\t\t\t\t<text class=\"picker-text\">{{ second }}秒</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</picker-view-column>\n\t\t\t\t\t</picker-view>\n\t\t\t\t</view>\n\n\n\t\t\t</view>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport { DateQuickOption} from '@/components/main-form/form_type.uts'\n\n\t// 定义模式类型\n\ttype DateTimeMode = 'datetime' | 'date' | 'time' | 'year' | 'year-month' | 'month' | 'day' | 'hour-minute' | 'hour-minute-second' | 'datetime-range' | 'date-range' | 'time-range'\n\n\texport default {\n\t\tname: \"main-datetime-picker\",\n\t\temits: ['cancel', 'confirm', 'change'],\n\t\tprops: {\n\t\t\t// 选择模式\n\t\t\tmode: {\n\t\t\t\ttype: String as PropType<DateTimeMode>,\n\t\t\t\tdefault: 'datetime' as DateTimeMode\n\t\t\t},\n\t\t\t// 标题\n\t\t\ttitle: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: '选择时间'\n\t\t\t},\n\t\t\t// 是否显示秒\n\t\t\tshowSeconds: {\n\t\t\t\ttype: Boolean,\n\t\t\t\tdefault: false\n\t\t\t},\n\t\t\t// 开始年份\n\t\t\tstartYear: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: () =>1970\n\t\t\t},\n\t\t\t// 结束年份\n\t\t\tendYear: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: () => 2035\n\t\t\t},\n\t\t\t// 快捷选项\n\t\t\tquickOptions: {\n\t\t\t\ttype: Array as PropType<DateQuickOption[]>,\n\t\t\t\tdefault: () => [] as DateQuickOption[]\n\t\t\t},\n\t\t\t// 选择器高度\n\t\t\theight: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 264\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\tconst now: Date = new Date()\n\t\t\treturn {\n\t\t\t\t// 控制弹窗显示\n\t\t\t\tvisible: false as boolean,\n\t\t\t\t// 当前日期\n\t\t\t\tcurrentDate: now as Date,\n\t\t\t\t// 区间值\n\t\t\t\trangeValues: [now, now] as Date[],\n\t\t\t\t// 当前区间索引\n\t\t\t\trangeIndex: 0 as number,\n\t\t\t\t// picker-view 的值\n\t\t\t\tpickerValue: [] as number[],\n\t\t\t\t// 当前快捷选项索引\n\t\t\t\tcurrentQuickIndex: -1 as number,\n\t\t\t\t// 年份列表\n\t\t\t\tyears: [] as string[],\n\t\t\t\t// 月份列表\n\t\t\t\tmonths: [] as string[],\n\t\t\t\t// 日期列表\n\t\t\t\tdays: [] as string[],\n\t\t\t\t// 小时列表\n\t\t\t\thours: [] as string[],\n\t\t\t\t// 分钟列表\n\t\t\t\tminutes: [] as string[],\n\t\t\t\t// 秒列表\n\t\t\t\tseconds: [] as string[],\n\t\t\t\t// 是否已初始化\n\t\t\t\tisInitialized: false as boolean\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 是否为区间模式\n\t\t\tisRange(): boolean {\n\t\t\t\treturn this.mode.includes('range')\n\t\t\t},\n\t\t\t// 是否显示年份\n\t\t\tshowYear(): boolean {\n\t\t\t\treturn ['datetime', 'date', 'year', 'year-month'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'date-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示月份\n\t\t\tshowMonth(): boolean {\n\t\t\t\treturn ['datetime', 'date', 'year-month', 'month'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'date-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示日期\n\t\t\tshowDay(): boolean {\n\t\t\t\treturn ['datetime', 'date', 'day'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'date-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示小时\n\t\t\tshowHour(): boolean {\n\t\t\t\treturn ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'time-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示分钟\n\t\t\tshowMinute(): boolean {\n\t\t\t\treturn ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||\n\t\t\t\t\t   ['datetime-range', 'time-range'].includes(this.mode)\n\t\t\t},\n\t\t\t// 是否显示秒\n\t\t\tshowSecond(): boolean {\n\t\t\t\treturn ['hour-minute-second'].includes(this.mode) ||\n\t\t\t\t\t   (this.showSeconds && ['datetime', 'time'].includes(this.mode)) ||\n\t\t\t\t\t   (this.showSeconds && ['datetime-range', 'time-range'].includes(this.mode))\n\t\t\t},\n\t\t\t// 指示器样式\n\t\t\tindicatorStyle(): string {\n\t\t\t\treturn 'height: 44px; border-top: 1px solid #eee; border-bottom: 1px solid #eee;'\n\t\t\t},\n\t\t\t// 显示标题\n\t\t\tdisplayTitle(): string {\n\t\t\t\tif (this.title != '选择时间') return this.title\n\n\t\t\t\tconst modeMap: UTSJSONObject = {\n\t\t\t\t\t'datetime': '选择日期时间',\n\t\t\t\t\t'date': '选择日期',\n\t\t\t\t\t'time': '选择时间',\n\t\t\t\t\t'year': '选择年份',\n\t\t\t\t\t'year-month': '选择年月',\n\t\t\t\t\t'month': '选择月份',\n\t\t\t\t\t'day': '选择日期',\n\t\t\t\t\t'hour-minute': '选择时间',\n\t\t\t\t\t'hour-minute-second': '选择时间',\n\t\t\t\t\t'datetime-range': '选择日期时间范围',\n\t\t\t\t\t'date-range': '选择日期范围',\n\t\t\t\t\t'time-range': '选择时间范围'\n\t\t\t\t}\n\n\t\t\t\tconst result = modeMap[this.mode] as string | null\n\t\t\t\treturn result != null ? result : '选择时间'\n\t\t\t},\n\t\t\t// 当前显示值\n\t\t\tcurrentDisplayValue(): string {\n\t\t\t\tif (this.isRange) {\n\t\t\t\t\tconst startFormatted: string = this.formatDate(this.rangeValues[0], this.mode.replace('-range', '') as DateTimeMode)\n\t\t\t\t\tconst endFormatted: string = this.formatDate(this.rangeValues[1], this.mode.replace('-range', '') as DateTimeMode)\n\t\t\t\t\treturn startFormatted + ' 至 ' + endFormatted\n\t\t\t\t} else {\n\t\t\t\t\treturn this.formatDate(this.currentDate, this.mode)\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {\n\t\t\tthis.initData()\n\t\t},\n\t\tmethods: {\n\t\t\t// 格式化日期\n\t\t\tformatDate(date: Date | null, type: DateTimeMode): string {\n\t\t\t\tif (date == null) return ''\n\n\t\t\t\ttry {\n\t\t\t\t\t// 确保是 Date 对象\n\t\t\t\t\tconst d: Date = date\n\t\t\t\t\tif (!this.validateDate(d)) return ''\n\n\t\t\t\t\tconst year: number = d.getFullYear()\n\t\t\t\t\tconst month: string = (d.getMonth() + 1).toString().padStart(2, '0')\n\t\t\t\t\tconst day: string = d.getDate().toString().padStart(2, '0')\n\t\t\t\t\tconst hour: string = d.getHours().toString().padStart(2, '0')\n\t\t\t\t\tconst minute: string = d.getMinutes().toString().padStart(2, '0')\n\t\t\t\t\tconst second: string = d.getSeconds().toString().padStart(2, '0')\n\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase 'datetime':\n\t\t\t\t\t\t\treturn `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`\n\t\t\t\t\t\tcase 'date':\n\t\t\t\t\t\t\treturn `${year}-${month}-${day}`\n\t\t\t\t\t\tcase 'time':\n\t\t\t\t\t\t\treturn `${hour}:${minute}${this.showSeconds ? ':' + second : ''}`\n\t\t\t\t\t\tcase 'year':\n\t\t\t\t\t\t\treturn `${year}`\n\t\t\t\t\t\tcase 'year-month':\n\t\t\t\t\t\t\treturn `${year}-${month}`\n\t\t\t\t\t\tcase 'month':\n\t\t\t\t\t\t\treturn `${month}`\n\t\t\t\t\t\tcase 'day':\n\t\t\t\t\t\t\treturn `${day}`\n\t\t\t\t\t\tcase 'hour-minute':\n\t\t\t\t\t\t\treturn `${hour}:${minute}`\n\t\t\t\t\t\tcase 'hour-minute-second':\n\t\t\t\t\t\t\treturn `${hour}:${minute}:${second}`\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Format date error:', error, date)\n\t\t\t\t\treturn ''\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 解析日期\n\t\t\tparseDate(value: Date | string | number | null): Date {\n\t\t\t\tif (value == null) return new Date()\n\n\t\t\t\ttry {\n\t\t\t\t\tlet date: Date | null = null\n\t\t\t\t\tif (value instanceof Date) {\n\t\t\t\t\t\t// 如果已经是 Date 对象，创建一个新的副本\n\t\t\t\t\t\tdate = new Date(value.getTime())\n\t\t\t\t\t} else if (typeof value == 'number' && !isNaN(value)) {\n\t\t\t\t\t\t// 数字类型，作为时间戳处理\n\t\t\t\t\t\tdate = new Date(value as number)\n\t\t\t\t\t} else if (typeof value == 'string') {\n\t\t\t\t\t\t// 字符串类型，需要解析\n\t\t\t\t\t\tif (value.includes('T')) {\n\t\t\t\t\t\t\t// ISO 格式字符串\n\t\t\t\t\t\t\tdate = new Date(value as string)\n\t\t\t\t\t\t} else if (value.includes('-') || value.includes('/')) {\n\t\t\t\t\t\t\t// 自定义格式字符串，手动解析\n\t\t\t\t\t\t\tconst parts: number[] = value.split(/[-\\s:/]/).map(p => parseInt(p))\n\t\t\t\t\t\t\tif (parts.length >= 3) {\n\t\t\t\t\t\t\t\tdate = new Date(\n\t\t\t\t\t\t\t\t\tparts[0], // year\n\t\t\t\t\t\t\t\t\tparts[1] - 1, // month\n\t\t\t\t\t\t\t\t\tparts[2], // day\n\t\t\t\t\t\t\t\t\tparts.length > 3 ? parts[3] : 0, // hour\n\t\t\t\t\t\t\t\t\tparts.length > 4 ? parts[4] : 0, // minute\n\t\t\t\t\t\t\t\t\tparts.length > 5 ? parts[5] : 0 // second\n\t\t\t\t\t\t\t\t)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 尝试作为时间戳字符串解析\n\t\t\t\t\t\t\tconst timestamp: number = parseInt(value)\n\t\t\t\t\t\t\tif (!isNaN(timestamp)) {\n\t\t\t\t\t\t\t\tdate = new Date(timestamp as number)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\treturn date != null && !isNaN(date.getTime()) ? date : new Date()\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Parse date error:', error)\n\t\t\t\t\treturn new Date()\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 验证日期\n\t\t\tvalidateDate(date: Date): boolean {\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\tconsole.warn('Invalid date:', date)\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tconst year: number = date.getFullYear()\n\t\t\t\tif (year < this.startYear || year > this.endYear) {\n\t\t\t\t\tconsole.warn('Date out of range:', date)\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshow(value?: Date | Date[] | string | number) {\n\t\t\t\tthis.visible = true\n\t\t\t\tthis.currentQuickIndex = -1\n\t\t\t\tthis.rangeIndex = 0\n\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\t// 处理区间值\n\t\t\t\t\t\tif (Array.isArray(value) && value.length == 2) {\n\t\t\t\t\t\t\tthis.rangeValues = value.map(v => this.parseDate(v))\n\t\t\t\t\t\t} else if (typeof value == 'string') {\n\t\t\t\t\t\t\t// 尝试解析字符串格式的日期\n\t\t\t\t\t\t\tconst date: Date = this.parseDate(value)\n\t\t\t\t\t\t\tthis.rangeValues = [date, date]\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tconst now: Date = new Date()\n\t\t\t\t\t\t\tthis.rangeValues = [now, now]\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 处理单个值\n\t\t\t\t\t\tthis.currentDate = this.parseDate(value)\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.initData()\n\t\t\t\t\t\tthis.updateCurrentValue()\n\t\t\t\t\t})\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Show picker error:', error)\n\t\t\t\t\tconst now: Date = new Date()\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\tthis.rangeValues = [now, now]\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.currentDate = now\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 隐藏选择器\n\t\t\thide() {\n\t\t\t\tthis.visible = false\n\t\t\t},\n\n\t\t\t// 点击遮罩层关闭弹窗\n\t\t\tonOverlayClick() {\n\t\t\t\tthis.hide()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 取消按钮点击事件\n\t\t\tonCancel() {\n\t\t\t\tthis.hide()\n\t\t\t\tthis.$emit('cancel')\n\t\t\t},\n\n\t\t\t// 创建值的Map对象\n\t\t\tcreateValueMap(date: Date): Map<string, any> {\n\t\t\t\tconst map = new Map<string, any>()\n\n\t\t\t\tif (this.showYear) {\n\t\t\t\t\tmap.set('year', date.getFullYear())\n\t\t\t\t}\n\t\t\t\tif (this.showMonth) {\n\t\t\t\t\tmap.set('month', date.getMonth() + 1)\n\t\t\t\t}\n\t\t\t\tif (this.showDay) {\n\t\t\t\t\tmap.set('day', date.getDate())\n\t\t\t\t}\n\t\t\t\tif (this.showHour) {\n\t\t\t\t\tmap.set('hour', date.getHours())\n\t\t\t\t}\n\t\t\t\tif (this.showMinute) {\n\t\t\t\t\tmap.set('minute', date.getMinutes())\n\t\t\t\t}\n\t\t\t\tif (this.showSecond) {\n\t\t\t\t\tmap.set('second', date.getSeconds())\n\t\t\t\t}\n\n\t\t\t\treturn map\n\t\t\t},\n\n\t\t\t// 确定按钮点击事件\n\t\t\tonConfirm() {\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\tif (!this.validateDate(this.rangeValues[0]) || !this.validateDate(this.rangeValues[1])) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '日期格式无效',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (this.rangeValues[1] < this.rangeValues[0]) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '结束时间不能早于开始时间',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst dateValues: Date[] = this.rangeValues.map(date => new Date(date.getTime()))\n\t\t\t\t\t\tconst mapValues: Map<string, any>[] = dateValues.map(date => this.createValueMap(date))\n\t\t\t\t\t\tconst formatted: string = dateValues.map(date => this.formatDate(date, this.mode.replace('-range', '') as DateTimeMode)).join(' 至 ')\n\n\t\t\t\t\t\tthis.$emit('confirm', {\n\t\t\t\t\t\t\tvalue: mapValues,\n\t\t\t\t\t\t\tformatted\n\t\t\t\t\t\t})\n\t\t\t\t\t} else {\n\t\t\t\t\t\tif (!this.validateDate(this.currentDate)) {\n\t\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\t\ttitle: '日期格式无效',\n\t\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst dateValue: Date = new Date(this.currentDate.getTime())\n\t\t\t\t\t\tconst mapValue: Map<string, any> = this.createValueMap(dateValue)\n\t\t\t\t\t\tconst formatted: string = this.formatDate(dateValue, this.mode)\n\n\t\t\t\t\t\tthis.$emit('confirm', {\n\t\t\t\t\t\t\tvalue: mapValue,\n\t\t\t\t\t\t\tformatted\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.hide()\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Confirm error:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '操作失败',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 通过索引选择快捷选项\n\t\t\tonQuickSelectByIndex(index: number) {\n\t\t\t\tif (index < 0 || index >= this.quickOptions.length) {\n\t\t\t\t\tconsole.warn('Invalid quick option index:', index)\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst option: DateQuickOption = this.quickOptions[index]\n\t\t\t\tthis.onQuickSelect(option, index)\n\t\t\t},\n\n\t\t\t// 快捷选项选择\n\t\t\tonQuickSelect(option: DateQuickOption, index: number) {\n\t\t\t\tthis.currentQuickIndex = index\n\t\t\t\tthis.rangeIndex = 0\n\n\t\t\t\ttry {\n\t\t\t\t\tif (this.isRange) {\n\t\t\t\t\t\t// 处理区间值\n\t\t\t\t\t\tif (Array.isArray(option.value)) {\n\t\t\t\t\t\t\t// Date 数组\n\t\t\t\t\t\t\tconst rangeValue: Date[] = option.value as Date[]\n\t\t\t\t\t\t\tif (rangeValue.length != 2) {\n\t\t\t\t\t\t\t\tconsole.warn('Quick option value should have 2 items for range mode:', option)\n\t\t\t\t\t\t\t\treturn\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.rangeValues = rangeValue.map(v => this.parseDate(v))\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 单个 Date，转换为区间\n\t\t\t\t\t\t\tconst date: Date = this.parseDate(option.value as Date)\n\t\t\t\t\t\t\tthis.rangeValues = [date, date]\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 处理单个值\n\t\t\t\t\t\tif (Array.isArray(option.value)) {\n\t\t\t\t\t\t\t// 如果是数组，取第一个值\n\t\t\t\t\t\t\tthis.currentDate = this.parseDate((option.value as Date[])[0])\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// 单个 Date 值\n\t\t\t\t\t\t\tthis.currentDate = this.parseDate(option.value as Date)\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\t\tthis.initData()\n\t\t\t\t\t\tthis.updateCurrentValue()\n\t\t\t\t\t})\n\n\t\t\t\t\tif (option.autoConfirm == true) {\n\t\t\t\t\t\tthis.onConfirm()\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('Quick select error:', error)\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '快捷选项格式无效',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// picker-view 变化事件\n\t\t\tonPickerChange(e: UniPickerViewChangeEvent) {\n\t\t\t\tthis.pickerValue = e.detail.value\n\t\t\t\tthis.currentQuickIndex = -1\n\t\t\t\tthis.updateDateFromValue()\n\t\t\t},\n\n\t\t\t// 区间选择切换\n\t\t\tonRangeChange(index: number) {\n\t\t\t\tif (this.rangeIndex == index) return\n\t\t\t\tthis.rangeIndex = index\n\t\t\t\tthis.$nextTick(() => {\n\t\t\t\t\tthis.updateCurrentValue()\n\t\t\t\t})\n\t\t\t},\n\n\t\t\t// 初始化数据\n\t\t\tinitData() {\n\t\t\t\tif (!this.isInitialized) {\n\t\t\t\t\t// 年\n\t\t\t\t\tthis.years = []\n\t\t\t\t\tfor (let i: number = this.startYear; i <= this.endYear; i++) {\n\t\t\t\t\t\tthis.years.push(i.toString())\n\t\t\t\t\t}\n\n\t\t\t\t\t// 月\n\t\t\t\t\tthis.months = []\n\t\t\t\t\tfor (let i: number = 1; i <= 12; i++) {\n\t\t\t\t\t\tthis.months.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 时\n\t\t\t\t\tthis.hours = []\n\t\t\t\t\tfor (let i: number = 0; i <= 23; i++) {\n\t\t\t\t\t\tthis.hours.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 分\n\t\t\t\t\tthis.minutes = []\n\t\t\t\t\tfor (let i: number = 0; i <= 59; i++) {\n\t\t\t\t\t\tthis.minutes.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 秒\n\t\t\t\t\tthis.seconds = []\n\t\t\t\t\tfor (let i: number = 0; i <= 59; i++) {\n\t\t\t\t\t\tthis.seconds.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.isInitialized = true\n\t\t\t\t}\n\n\t\t\t\t// 更新日期列表\n\t\t\t\tthis.updateDaysList()\n\t\t\t},\n\n\t\t\t// 更新日期列表（根据当前年月动态计算）\n\t\t\tupdateDaysList() {\n\t\t\t\tconst date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate\n\t\t\t\tconst year: number = date.getFullYear()\n\t\t\t\tconst month: number = date.getMonth() + 1\n\n\t\t\t\t// 获取当月天数，处理闰年和不同月份的天数差异\n\t\t\t\tconst daysInMonth: number = this.getDaysInMonth(year, month)\n\n\t\t\t\tthis.days = []\n\t\t\t\tfor (let i: number = 1; i <= daysInMonth; i++) {\n\t\t\t\t\tthis.days.push(i.toString().padStart(2, '0'))\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 获取指定年月的天数\n\t\t\tgetDaysInMonth(year: number, month: number): number {\n\t\t\t\t// 使用 Date 构造函数的特性：月份设为下个月的第0天，会自动返回当月最后一天\n\t\t\t\treturn new Date(year, month, 0).getDate()\n\t\t\t},\n\n\t\t\t// 更新当前值\n\t\t\tupdateCurrentValue() {\n\t\t\t\tconst date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate\n\t\t\t\tif (isNaN(date.getTime())) {\n\t\t\t\t\tconsole.warn('Invalid date in updateCurrentValue:', date)\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\t// 确保日期列表是最新的\n\t\t\t\tthis.updateDaysList()\n\n\t\t\t\tconst values: number[] = []\n\n\t\t\t\tif (this.showYear) {\n\t\t\t\t\tconst yearIndex: number = this.years.findIndex(y => parseInt(y) == date.getFullYear())\n\t\t\t\t\tvalues.push(yearIndex >= 0 ? yearIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showMonth) {\n\t\t\t\t\tconst monthStr: string = (date.getMonth() + 1).toString().padStart(2, '0')\n\t\t\t\t\tconst monthIndex: number = this.months.findIndex(m => m == monthStr)\n\t\t\t\t\tvalues.push(monthIndex >= 0 ? monthIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showDay) {\n\t\t\t\t\tconst dayStr: string = date.getDate().toString().padStart(2, '0')\n\t\t\t\t\tconst dayIndex: number = this.days.findIndex(d => d == dayStr)\n\t\t\t\t\t// 确保索引在有效范围内\n\t\t\t\t\tif (dayIndex >= 0 && dayIndex < this.days.length) {\n\t\t\t\t\t\tvalues.push(dayIndex)\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果找不到对应的日期，使用当月最后一天的索引\n\t\t\t\t\t\tvalues.push(this.days.length - 1)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (this.showHour) {\n\t\t\t\t\tconst hourStr: string = date.getHours().toString().padStart(2, '0')\n\t\t\t\t\tconst hourIndex: number = this.hours.findIndex(h => h == hourStr)\n\t\t\t\t\tvalues.push(hourIndex >= 0 ? hourIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showMinute) {\n\t\t\t\t\tconst minuteStr: string = date.getMinutes().toString().padStart(2, '0')\n\t\t\t\t\tconst minuteIndex: number = this.minutes.findIndex(m => m == minuteStr)\n\t\t\t\t\tvalues.push(minuteIndex >= 0 ? minuteIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tif (this.showSecond) {\n\t\t\t\t\tconst secondStr: string = date.getSeconds().toString().padStart(2, '0')\n\t\t\t\t\tconst secondIndex: number = this.seconds.findIndex(s => s == secondStr)\n\t\t\t\t\tvalues.push(secondIndex >= 0 ? secondIndex : 0)\n\t\t\t\t}\n\n\t\t\t\tthis.pickerValue = [...values]\n\t\t\t},\n\n\t\t\t// 从picker值更新日期\n\t\t\tupdateDateFromValue() {\n\t\t\t\tif (!Array.isArray(this.pickerValue)) return\n\n\t\t\t\tlet index: number = 0\n\t\t\t\tlet year: number = this.currentDate.getFullYear()\n\t\t\t\tlet month: number = this.currentDate.getMonth()\n\t\t\t\tlet day: number = this.currentDate.getDate()\n\t\t\t\tlet hour: number = this.currentDate.getHours()\n\t\t\t\tlet minute: number = this.currentDate.getMinutes()\n\t\t\t\tlet second: number = this.currentDate.getSeconds()\n\n\t\t\t\t// 记录原始的年月，用于检测是否发生变化\n\t\t\t\tconst originalYear: number = year\n\t\t\t\tconst originalMonth: number = month\n\n\t\t\t\tif (this.showYear && index < this.pickerValue.length) {\n\t\t\t\t\tyear = parseInt(this.years[this.pickerValue[index]])\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showMonth && index < this.pickerValue.length) {\n\t\t\t\t\tmonth = parseInt(this.months[this.pickerValue[index]]) - 1\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\t// 检查年月是否发生变化，如果是，需要重新计算日期列表\n\t\t\t\tconst yearMonthChanged: boolean = (year != originalYear || month != originalMonth)\n\n\t\t\t\tif (yearMonthChanged) {\n\t\t\t\t\t// 年月发生变化，需要重新计算当月天数\n\t\t\t\t\tconst daysInNewMonth: number = this.getDaysInMonth(year, month + 1)\n\n\t\t\t\t\t// 如果当前日期超过了新月份的最大天数，调整为最大天数\n\t\t\t\t\tif (day > daysInNewMonth) {\n\t\t\t\t\t\tday = daysInNewMonth\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新日期列表\n\t\t\t\t\tthis.days = []\n\t\t\t\t\tfor (let i: number = 1; i <= daysInNewMonth; i++) {\n\t\t\t\t\t\tthis.days.push(i.toString().padStart(2, '0'))\n\t\t\t\t\t}\n\n\t\t\t\t\t// 更新 picker-view 的日期索引，确保在有效范围内\n\t\t\t\t\tif (this.showDay && index < this.pickerValue.length) {\n\t\t\t\t\t\tconst currentDayIndex: number = this.pickerValue[index]\n\t\t\t\t\t\tif (currentDayIndex >= this.days.length) {\n\t\t\t\t\t\t\t// 如果当前索引超出新月份的天数范围，调整为最后一天\n\t\t\t\t\t\t\tthis.pickerValue[index] = this.days.length - 1\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (this.showDay && index < this.pickerValue.length) {\n\t\t\t\t\tconst dayIndex: number = this.pickerValue[index]\n\t\t\t\t\t// 检查索引是否在有效范围内\n\t\t\t\t\tif (dayIndex >= 0 && dayIndex < this.days.length) {\n\t\t\t\t\t\tconst selectedDay: number = parseInt(this.days[dayIndex])\n\t\t\t\t\t\tday = selectedDay\n\t\t\t\t\t} else {\n\t\t\t\t\t\t// 如果索引超出范围，使用当月最后一天\n\t\t\t\t\t\tday = this.days.length\n\t\t\t\t\t}\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showHour && index < this.pickerValue.length) {\n\t\t\t\t\thour = parseInt(this.hours[this.pickerValue[index]])\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showMinute && index < this.pickerValue.length) {\n\t\t\t\t\tminute = parseInt(this.minutes[this.pickerValue[index]])\n\t\t\t\t\tindex++\n\t\t\t\t}\n\n\t\t\t\tif (this.showSecond && index < this.pickerValue.length) {\n\t\t\t\t\tsecond = parseInt(this.seconds[this.pickerValue[index]])\n\t\t\t\t}\n\n\t\t\t\tconst newDate: Date = new Date(year, month, day, hour, minute, second)\n\n\t\t\t\tif (this.isRange) {\n\t\t\t\t\tthis.rangeValues[this.rangeIndex] = newDate\n\t\t\t\t\tif (this.rangeIndex == 0 && this.rangeValues[1] < newDate) {\n\t\t\t\t\t\tthis.rangeValues[1] = new Date(newDate.getTime())\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.currentDate = newDate\n\t\t\t\t}\n\n\t\t\t\t// 如果年月没有变化，只需要更新日期列表\n\t\t\t\tif (!yearMonthChanged) {\n\t\t\t\t\tthis.updateDaysList()\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t</script>\n\n\t<style>\n\t\t/* 遮罩层 */\n\t\t.picker-overlay {\n\t\t\tposition: fixed;\n\t\t\ttop: 0;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground-color: rgba(0, 0, 0, 0.4);\n\t\t\tz-index: 999;\n\t\t}\n\n\t\t.picker-modal {\n\t\t\tposition: fixed;\n\t\t\tleft: 0;\n\t\t\tright: 0;\n\t\t\tbottom: 0;\n\t\t\tbackground-color: #ffffff;\n\t\t\tz-index: 1000;\n\t\t}\n\n\t\t.datetime-picker-container {\n\t\t\twidth: 100%;\n\t\t\tbackground-color: #ffffff;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t}\n\n\t\t/* 导航栏样式 */\n\t\t.navbar {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t\tflex-direction: row;\n\t\t\tjustify-content: space-between;\n\t\t\theight: 88rpx;\n\t\t\tpadding: 0 30rpx;\n\t\t\tbackground-color: #ffffff;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\n\t\t.nav-btn {\n\t\t\tmin-width: 80rpx;\n\t\t\ttext-align: center;\n\t\t}\n\n\t\t.cancel-btn {\n\t\t\tcolor: #999;\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t.confirm-btn-container {\n\t\t\tmin-width: 80rpx;\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.confirm-btn {\n\t\t\tcolor: #576B95;\n\t\t\tfont-weight: 400;\n\t\t\tfont-size: 32rpx;\n\t\t}\n\n\t\t.nav-title {\n\t\t\tcolor: #333;\n\t\t\tfont-weight: 400;\n\t\t\tfont-size: 32rpx;\n\t\t\ttext-align: center;\n\t\t\tflex: 1;\n\t\t}\n\n\t\t/* 快捷选项样式 */\n\t\t.quick-options-container {\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\n\t\t.quick-options-scroll {\n\t\t\theight: 80rpx;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.quick-options {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tpadding: 10rpx 20rpx;\n\t\t\theight: 60rpx;\n\t\t}\n\n\t\t.quick-item {\n\t\t\tpadding: 8rpx 16rpx;\n\t\t\tmargin-right: 12rpx;\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #666;\n\t\t\tbackground-color: #f5f5f5;\n\t\t\tborder-radius: 20rpx;\n\t\t\twhite-space: nowrap;\n\t\t}\n\n\t\t.quick-item-active {\n\t\t\tcolor: #ffffff;\n\t\t\tbackground-color: #007AFF;\n\t\t}\n\n\t\t/* 区间选择标签样式 */\n\t\t.range-tabs {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: row;\n\t\t\talign-items: center;\n\t\t\tpadding: 20rpx;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t}\n\n\t\t.range-tab {\n\t\t\tflex: 1;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #666;\n\t\t\tpadding: 10rpx 0;\n\t\t}\n\n\t\t.range-tab-active {\n\t\t\tcolor: #007AFF;\n\t\t\tposition: relative;\n\t\t}\n\n\t\t/* picker-view 样式 */\n\t\t.picker-body {\n\t\t\tposition: relative;\n\t\t}\n\n\t\t.picker-view {\n\t\t\twidth: 100%;\n\t\t\theight: 264px;\n\t\t}\n\n\t\t.picker-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\theight: 44px;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.picker-text {\n\t\t\tfont-size: 16px;\n\t\t\tcolor: #333;\n\t\t}\n\n\n\t</style>", "<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"color-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">颜色选择</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色系列选择按钮 -->\r\n\t\t\t\t<view class=\"color-series-section\">\r\n\t\t\t\t\t<view class=\"color-series-buttons\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(series, index) in colorSeriesList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"series-button\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'active': selectedSeriesIndex == index,\r\n\t\t\t\t\t\t\t\t'random-button': index == 0,\r\n\t\t\t\t\t\t\t\t'normal-button': index != 0\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundColor: series.color }\"\r\n\t\t\t\t\t\t\t@click=\"onSeriesSelect(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"series-text\">{{ series.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色方块列表 -->\r\n\t\t\t\t<view class=\"color-grid-section\">\r\n\t\t\t\t\t<view class=\"color-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(color, index) in colorList\" :key=\"index\" class=\"color-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedColorIndex == index }\" :style=\"{ backgroundColor: color }\"\r\n\t\t\t\t\t\t\t@click=\"onColorSelect(index)\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预览和透明度选择区域 -->\r\n\t\t\t\t<view class=\"preview-opacity-section\">\r\n\t\t\t\t\t<view class=\"preview-area\" @click=\"showRGBPicker\">\r\n\t\t\t\t\t\t<view class=\"preview-color\" :style=\"{ backgroundColor: finalColor }\"></view>\r\n\t\t\t\t\t\t<text class=\"rgba-text\">{{ finalColor }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"opacity-area\">\r\n\t\t\t\t\t\t<text class=\"opacity-label\">透明度</text>\r\n\t\t\t\t\t\t<view class=\"opacity-button\" @click=\"showOpacityPicker\">\r\n\t\t\t\t\t\t\t<text class=\"opacity-value\">{{ Math.round(opacity * 100) }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- RGB设置弹窗 -->\r\n\t\t\t\t<view v-if=\"showRGBModal\" class=\"rgb-modal-overlay\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t<view class=\"rgb-modal\" @click=\"onRGBModalClick\">\r\n\t\t\t\t\t\t<view class=\"rgb-modal-header\">\r\n\t\t\t\t\t\t\t<text class=\"rgb-modal-title\">RGB颜色设置</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-preview-section\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-preview-color\" :style=\"{ backgroundColor: tempRGBColor }\"></view>\r\n\t\t\t\t\t\t\t<text class=\"rgb-preview-text\">{{ tempRGBColor }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-controls\">\r\n\t\t\t\t\t\t\t<!-- R值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">R</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempR\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempRChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempR.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempRInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- G值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">G</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempG\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempGChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempG.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempGInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- B值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">B</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempB\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempBChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempB.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempBInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-modal-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-cancel\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">取消</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-confirm\" @click=\"confirmRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">确定</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义颜色类型\r\n\ttype ColorInfo = {\r\n\t\tr : number,\r\n\t\tg : number,\r\n\t\tb : number\r\n\t}\r\n\ttype RGBAValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number,\r\n\t  a: number\r\n\t}\r\n\ttype RGBValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number\r\n\t}\r\n\ttype ColorSeries = {\r\n\t  name: string,\r\n\t  color: string\r\n\t}\r\n\texport default {\r\n\t\tname: \"main-color-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的颜色系列索引\r\n\t\t\t\tselectedSeriesIndex: 0 as number,\r\n\t\t\t\t// 透明度，范围0-1\r\n\t\t\t\topacity: 1.0 as number,\r\n\t\t\t\t// 当前选中的颜色索引\r\n\t\t\t\tselectedColorIndex: 0 as number,\r\n\t\t\t\t// 基础颜色（可以根据需要修改）\r\n\t\t\t\tbaseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,\r\n\t\t\t\t// 随机色种子，用于重新生成随机色\r\n\t\t\t\trandomSeed: 0 as number,\r\n\t\t\t\t// RGB设置弹窗相关\r\n\t\t\t\tshowRGBModal: false as boolean,\r\n\t\t\t\ttempR: 255 as number,\r\n\t\t\t\ttempG: 0 as number,\r\n\t\t\t\ttempB: 0 as number,\r\n\t\t\t\t// 自定义颜色\r\n\t\t\t\tcustomColor: \"\" as string\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 颜色系列列表\r\n\t\t\tcolorSeriesList(): ColorSeries[] {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{ name: \"随机色\", color: \"#FF6B35\" },\r\n\t\t\t\t\t{ name: \"黑白灰\", color: \"#808080\" },\r\n\t\t\t\t\t{ name: \"红色\", color: \"#FF4444\" },\r\n\t\t\t\t\t{ name: \"橙色\", color: \"#FF8844\" },\r\n\t\t\t\t\t{ name: \"黄色\", color: \"#FFDD44\" },\r\n\t\t\t\t\t{ name: \"绿色\", color: \"#44FF44\" },\r\n\t\t\t\t\t{ name: \"青色\", color: \"#44FFFF\" },\r\n\t\t\t\t\t{ name: \"蓝色\", color: \"#4444FF\" },\r\n\t\t\t\t\t{ name: \"紫色\", color: \"#AA44FF\" },\r\n\t\t\t\t\t{ name: \"粉色\", color: \"#FF88CC\" },\r\n\t\t\t\t\t{ name: \"棕色\", color: \"#AA6644\" }\r\n\t\t\t\t]\r\n\t\t\t},\r\n\r\n\t\t\t// 根据选中的系列生成120个颜色（10行12列）\r\n\t\t\tcolorList() : string[] {\r\n\t\t\t\tconst colors : string[] = []\r\n\r\n\t\t\t\tfor (let i = 0; i < 120; i++) {\r\n\t\t\t\t\tconst row = Math.floor(i / 12) // 当前行（0-9）\r\n\t\t\t\t\tconst col = i % 12 // 当前列（0-11）\r\n\r\n\t\t\t\t\t// 计算位置因子\r\n\t\t\t\t\tconst rowFactor = row / 9.0 // 行因子 0-1\r\n\t\t\t\t\tconst colFactor = col / 11.0 // 列因子 0-1\r\n\r\n\t\t\t\t\t// 基于选中的系列索引确定颜色系列\r\n\t\t\t\t\tlet r: number, g: number, b: number\r\n\r\n\t\t\t\t\tif (this.selectedSeriesIndex == 0) {\r\n\t\t\t\t\t\t// 随机色系列 - 每个方块完全随机的RGB值\r\n\t\t\t\t\t\tconst seed1 = (row * 12 + col + this.randomSeed) * 0.1\r\n\t\t\t\t\t\tconst seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13\r\n\t\t\t\t\t\tconst seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17\r\n\t\t\t\t\t\tr = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tg = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tb = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 1) {\r\n\t\t\t\t\t\t// 黑白灰系列 - 更细腻的灰度变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变\r\n\t\t\t\t\t\tconst grayValue = Math.round(totalFactor * 255)\r\n\t\t\t\t\t\tr = grayValue\r\n\t\t\t\t\t\tg = grayValue\r\n\t\t\t\t\t\tb = grayValue\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 2) {\r\n\t\t\t\t\t\t// 红色系列 - 更丰富的红色变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\t\t\t\t\t\tconst brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围\r\n\t\t\t\t\t\tconst saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高\r\n\t\t\t\t\t\tr = Math.round(brightness * 255)\r\n\t\t\t\t\t\tg = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t\tb = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他颜色系列 - 确保包含纯色且避免黑色\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\r\n\t\t\t\t\t\t// 根据系列索引确定基础色相\r\n\t\t\t\t\t\tlet baseHue: number\r\n\t\t\t\t\t\tif (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色\r\n\t\t\t\t\t\telse baseHue = 25 // 棕色\r\n\r\n\t\t\t\t\t\t// 色相微调：在基础色相±10度范围内变化\r\n\t\t\t\t\t\tconst hue = baseHue + (colFactor - 0.5) * 20\r\n\r\n\t\t\t\t\t\t// 创造三种类型的颜色变化\r\n\t\t\t\t\t\tif (totalFactor < 0.4) {\r\n\t\t\t\t\t\t\t// 前40%：深色调 - 高饱和度，中低明度（避免太暗）\r\n\t\t\t\t\t\t\tconst localFactor = totalFactor / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 + localFactor * 0.2 // 0.8-1.0\r\n\t\t\t\t\t\t\tconst value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else if (totalFactor < 0.6) {\r\n\t\t\t\t\t\t\t// 中20%：纯色调 - 最高饱和度，最佳明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.4) / 0.2\r\n\t\t\t\t\t\t\tconst saturation = 1.0 // 最高饱和度\r\n\t\t\t\t\t\t\tconst value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 后40%：浅色调 - 降低饱和度，保持高明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.6) / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）\r\n\t\t\t\t\t\t\tconst value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 确保RGB值在0-255范围内\r\n\t\t\t\t\tr = Math.max(0, Math.min(255, r))\r\n\t\t\t\t\tg = Math.max(0, Math.min(255, g))\r\n\t\t\t\t\tb = Math.max(0, Math.min(255, b))\r\n\r\n\t\t\t\t\tcolors.push(`rgb(${r}, ${g}, ${b})`)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn colors\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 最终的RGBA颜色值\r\n\t\t\tfinalColor() : string {\r\n\t\t\t\t// 优先使用自定义颜色\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\t// 提取RGB值并添加透明度\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tconst r = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tconst g = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tconst b = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t\treturn `rgba(${r}, ${g}, ${b}, ${this.opacity})`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn `rgba(255, 0, 0, ${this.opacity})`\r\n\t\t\t},\r\n\r\n\t\t\t// 临时RGB颜色预览\r\n\t\t\ttempRGBColor() : string {\r\n\t\t\t\treturn `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 颜色系列选择事件\r\n\t\t\tonSeriesSelect(index: number) {\r\n\t\t\t\tthis.selectedSeriesIndex = index\r\n\t\t\t\tthis.selectedColorIndex = 0 // 重置选中的颜色\r\n\t\t\t\tthis.customColor = \"\" // 清除自定义颜色\r\n\r\n\t\t\t\t// 如果选择的是随机色系列，生成新的随机种子\r\n\t\t\t\tif (index == 0) { // 随机色现在是第1个按钮，索引为0\r\n\t\t\t\t\tthis.randomSeed = Math.floor(Math.random() * 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示透明度选择器\r\n\t\t\tshowOpacityPicker() {\r\n\t\t\t\tconst opacityOptions = [\r\n\t\t\t\t\t'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',\r\n\t\t\t\t\t'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'\r\n\t\t\t\t]\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: opacityOptions,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst selectedOpacity = (100 - res.tapIndex * 5) / 100\r\n\t\t\t\t\t\tthis.opacity = selectedOpacity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 颜色选择事件\r\n\t\t\tonColorSelect(index : number) {\r\n\t\t\t\tthis.selectedColorIndex = index\r\n\t\t\t\t// 清除自定义颜色，使用新选中的颜色\r\n\t\t\t\tthis.customColor = \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 显示RGB设置弹窗\r\n\t\t\tshowRGBPicker() {\r\n\t\t\t\t// 获取当前颜色的RGB值（优先使用自定义颜色）\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tthis.tempR = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tthis.tempG = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tthis.tempB = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.showRGBModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭RGB设置弹窗\r\n\t\t\tcloseRGBPicker() {\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// RGB弹窗点击事件（阻止冒泡）\r\n\t\t\tonRGBModalClick() {\r\n\t\t\t\t// 空方法，用于阻止事件冒泡\r\n\t\t\t},\r\n\r\n\t\t\t// 确认RGB设置\r\n\t\t\tconfirmRGBPicker() {\r\n\t\t\t\t// 设置自定义颜色\r\n\t\t\t\tthis.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// R值滑块变化\r\n\t\t\tonTempRChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempR = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// G值滑块变化\r\n\t\t\tonTempGChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempG = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// B值滑块变化\r\n\t\t\tonTempBChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempB = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// R值输入框变化\r\n\t\t\tonTempRInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempR = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// G值输入框变化\r\n\t\t\tonTempGInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempG = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// B值输入框变化\r\n\t\t\tonTempBInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempB = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst rgbaValues = this.getRGBAValues()\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tcolor: this.finalColor,\r\n\t\t\t\t\trgba: rgbaValues,\r\n\t\t\t\t\thex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)\r\n\t\t\t\t})\r\n\t\t\t},\r\n \r\n\t\t\t// 获取RGBA数值\r\n\t\t\tgetRGBAValues() : RGBAValues {\r\n\t\t\t\tconst rgbaMatch = this.finalColor.match(/rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)/)\r\n\t\t\t\tif (rgbaMatch != null) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tr: parseInt(rgbaMatch[1] as string),\r\n\t\t\t\t\t\tg: parseInt(rgbaMatch[2] as string),\r\n\t\t\t\t\t\tb: parseInt(rgbaMatch[3] as string),\r\n\t\t\t\t\t\ta: parseFloat(rgbaMatch[4] as string)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn { r: 255, g: 0, b: 0, a: 1.0 }\r\n\t\t\t},\r\n\r\n\t\t\t// HSV转RGB\r\n\t\t\thsvToRgb(h: number, s: number, v: number): RGBValues {\r\n\t\t\t\tconst c: number = v * s\r\n\t\t\t\tconst x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))\r\n\t\t\t\tconst m: number = v - c\r\n\r\n\t\t\t\tlet r: number = 0.0\r\n\t\t\t\tlet g: number = 0.0\r\n\t\t\t\tlet b: number = 0.0\r\n\r\n\t\t\t\tif (h >= 0 && h < 60) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 60 && h < 120) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 120 && h < 180) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = x\r\n\t\t\t\t} else if (h >= 180 && h < 240) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 240 && h < 300) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 300 && h < 360) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = x\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst result: RGBValues = {\r\n\t\t\t\t\tr: Math.round((r + m) * 255.0),\r\n\t\t\t\t\tg: Math.round((g + m) * 255.0),\r\n\t\t\t\t\tb: Math.round((b + m) * 255.0)\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t},\r\n\r\n\t\t\t// RGB转十六进制\r\n\t\t\trgbToHex(r: number, g: number, b: number): string {\r\n\t\t\t\tconst toHex = (value: number): string => {\r\n\t\t\t\t\tconst hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)\r\n\t\t\t\t\treturn hex.length == 1 ? '0' + hex : hex\r\n\t\t\t\t}\r\n\t\t\t\treturn '#' + toHex(r) + toHex(g) + toHex(b)\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-top-left-radius: 20rpx;\r\n\t\tborder-top-right-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.color-picker-container {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 区域标题样式 */\r\n\t.section-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 颜色系列选择区域 */\r\n\t.color-series-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-series-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.series-button {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2px solid transparent;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 随机色按钮：两个普通按钮宽度 + 间距 */\r\n\t.random-button {\r\n\t\twidth: 220rpx;\r\n\t}\r\n\r\n\t/* 其他按钮正常宽度 */\r\n\t.normal-button {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.series-button.active {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t.series-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t/* 颜色网格区域 */\r\n\t.color-grid-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\r\n\t.color-item {\r\n\t\twidth: 55rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 2px solid transparent;\r\n\t\tmargin-bottom: 4px;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-grow: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.color-item.selected {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t/* 预览和透明度选择区域 */\r\n\t.preview-opacity-section {\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.preview-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.rgba-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.opacity-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.opacity-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.opacity-button {\r\n\t\tbackground-color: #007aff;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.opacity-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* RGB设置弹窗样式 */\r\n\t.rgb-modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.rgb-modal {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-height: 800rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.rgb-preview-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.rgb-preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.rgb-preview-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t.rgb-controls {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.rgb-control-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-label {\r\n\t\twidth: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.rgb-slider {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 15rpx;\r\n\t}\r\n\r\n\t.rgb-input {\r\n\t\twidth: 120rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.rgb-button {\r\n\t\twidth: 45%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.rgb-cancel {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\t.rgb-confirm {\r\n\t\tbackground-color: #007aff;\r\n\t}\r\n\r\n\t.rgb-button-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rgb-cancel .rgb-button-text {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.rgb-confirm .rgb-button-text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n</style>", "<template>\r\n\t<!-- 弹窗遮罩层 -->\r\n\t<view v-if=\"visible\" class=\"picker-overlay\" @click=\"onOverlayClick\">\r\n\t\t<view class=\"picker-modal\" @click.stop=\"\">\r\n\t\t\t<view class=\"color-picker-container\">\r\n\t\t\t\t<!-- 导航栏 -->\r\n\t\t\t\t<view class=\"navbar\">\r\n\t\t\t\t\t<text class=\"nav-btn cancel-btn\" @click=\"onCancel\">取消</text>\r\n\t\t\t\t\t<text class=\"nav-title\">颜色选择</text>\r\n\t\t\t\t\t<view class=\"confirm-btn-container\">\r\n\t\t\t\t\t\t<text class=\"nav-btn confirm-btn\" @click=\"onConfirm\">确定</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色系列选择按钮 -->\r\n\t\t\t\t<view class=\"color-series-section\">\r\n\t\t\t\t\t<view class=\"color-series-buttons\">\r\n\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\tv-for=\"(series, index) in colorSeriesList\"\r\n\t\t\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t\t\tclass=\"series-button\"\r\n\t\t\t\t\t\t\t:class=\"{\r\n\t\t\t\t\t\t\t\t'active': selectedSeriesIndex == index,\r\n\t\t\t\t\t\t\t\t'random-button': index == 0,\r\n\t\t\t\t\t\t\t\t'normal-button': index != 0\r\n\t\t\t\t\t\t\t}\"\r\n\t\t\t\t\t\t\t:style=\"{ backgroundColor: series.color }\"\r\n\t\t\t\t\t\t\t@click=\"onSeriesSelect(index)\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<text class=\"series-text\">{{ series.name }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 颜色方块列表 -->\r\n\t\t\t\t<view class=\"color-grid-section\">\r\n\t\t\t\t\t<view class=\"color-grid\">\r\n\t\t\t\t\t\t<view v-for=\"(color, index) in colorList\" :key=\"index\" class=\"color-item\"\r\n\t\t\t\t\t\t\t:class=\"{ 'selected': selectedColorIndex == index }\" :style=\"{ backgroundColor: color }\"\r\n\t\t\t\t\t\t\t@click=\"onColorSelect(index)\">\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- 预览和透明度选择区域 -->\r\n\t\t\t\t<view class=\"preview-opacity-section\">\r\n\t\t\t\t\t<view class=\"preview-area\" @click=\"showRGBPicker\">\r\n\t\t\t\t\t\t<view class=\"preview-color\" :style=\"{ backgroundColor: finalColor }\"></view>\r\n\t\t\t\t\t\t<text class=\"rgba-text\">{{ finalColor }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"opacity-area\">\r\n\t\t\t\t\t\t<text class=\"opacity-label\">透明度</text>\r\n\t\t\t\t\t\t<view class=\"opacity-button\" @click=\"showOpacityPicker\">\r\n\t\t\t\t\t\t\t<text class=\"opacity-value\">{{ Math.round(opacity * 100) }}%</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<!-- RGB设置弹窗 -->\r\n\t\t\t\t<view v-if=\"showRGBModal\" class=\"rgb-modal-overlay\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t<view class=\"rgb-modal\" @click=\"onRGBModalClick\">\r\n\t\t\t\t\t\t<view class=\"rgb-modal-header\">\r\n\t\t\t\t\t\t\t<text class=\"rgb-modal-title\">RGB颜色设置</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-preview-section\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-preview-color\" :style=\"{ backgroundColor: tempRGBColor }\"></view>\r\n\t\t\t\t\t\t\t<text class=\"rgb-preview-text\">{{ tempRGBColor }}</text>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-controls\">\r\n\t\t\t\t\t\t\t<!-- R值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">R</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempR\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempRChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempR.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempRInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- G值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">G</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempG\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempGChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempG.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempGInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<!-- B值控制 -->\r\n\t\t\t\t\t\t\t<view class=\"rgb-control-item\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-label\">B</text>\r\n\t\t\t\t\t\t\t\t<slider class=\"rgb-slider\" :min=\"0\" :max=\"255\" :step=\"1\" :value=\"tempB\"\r\n\t\t\t\t\t\t\t\t\t@change=\"onTempBChange\" />\r\n\t\t\t\t\t\t\t\t<input class=\"rgb-input\" type=\"number\" :value=\"tempB.toString()\"\r\n\t\t\t\t\t\t\t\t\t@input=\"onTempBInput\" placeholder=\"0-255\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t<view class=\"rgb-modal-buttons\">\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-cancel\" @click=\"closeRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">取消</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"rgb-button rgb-confirm\" @click=\"confirmRGBPicker\">\r\n\t\t\t\t\t\t\t\t<text class=\"rgb-button-text\">确定</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// 定义颜色类型\r\n\ttype ColorInfo = {\r\n\t\tr : number,\r\n\t\tg : number,\r\n\t\tb : number\r\n\t}\r\n\ttype RGBAValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number,\r\n\t  a: number\r\n\t}\r\n\ttype RGBValues = {\r\n\t  r: number,\r\n\t  g: number,\r\n\t  b: number\r\n\t}\r\n\ttype ColorSeries = {\r\n\t  name: string,\r\n\t  color: string\r\n\t}\r\n\texport default {\r\n\t\tname: \"main-color-picker\",\r\n\t\temits: ['cancel', 'confirm'],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 控制弹窗显示\r\n\t\t\t\tvisible: false as boolean,\r\n\t\t\t\t// 当前选中的颜色系列索引\r\n\t\t\t\tselectedSeriesIndex: 0 as number,\r\n\t\t\t\t// 透明度，范围0-1\r\n\t\t\t\topacity: 1.0 as number,\r\n\t\t\t\t// 当前选中的颜色索引\r\n\t\t\t\tselectedColorIndex: 0 as number,\r\n\t\t\t\t// 基础颜色（可以根据需要修改）\r\n\t\t\t\tbaseColor: { r: 255.0, g: 0.0, b: 0.0 } as ColorInfo,\r\n\t\t\t\t// 随机色种子，用于重新生成随机色\r\n\t\t\t\trandomSeed: 0 as number,\r\n\t\t\t\t// RGB设置弹窗相关\r\n\t\t\t\tshowRGBModal: false as boolean,\r\n\t\t\t\ttempR: 255 as number,\r\n\t\t\t\ttempG: 0 as number,\r\n\t\t\t\ttempB: 0 as number,\r\n\t\t\t\t// 自定义颜色\r\n\t\t\t\tcustomColor: \"\" as string\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 颜色系列列表\r\n\t\t\tcolorSeriesList(): ColorSeries[] {\r\n\t\t\t\treturn [\r\n\t\t\t\t\t{ name: \"随机色\", color: \"#FF6B35\" },\r\n\t\t\t\t\t{ name: \"黑白灰\", color: \"#808080\" },\r\n\t\t\t\t\t{ name: \"红色\", color: \"#FF4444\" },\r\n\t\t\t\t\t{ name: \"橙色\", color: \"#FF8844\" },\r\n\t\t\t\t\t{ name: \"黄色\", color: \"#FFDD44\" },\r\n\t\t\t\t\t{ name: \"绿色\", color: \"#44FF44\" },\r\n\t\t\t\t\t{ name: \"青色\", color: \"#44FFFF\" },\r\n\t\t\t\t\t{ name: \"蓝色\", color: \"#4444FF\" },\r\n\t\t\t\t\t{ name: \"紫色\", color: \"#AA44FF\" },\r\n\t\t\t\t\t{ name: \"粉色\", color: \"#FF88CC\" },\r\n\t\t\t\t\t{ name: \"棕色\", color: \"#AA6644\" }\r\n\t\t\t\t]\r\n\t\t\t},\r\n\r\n\t\t\t// 根据选中的系列生成120个颜色（10行12列）\r\n\t\t\tcolorList() : string[] {\r\n\t\t\t\tconst colors : string[] = []\r\n\r\n\t\t\t\tfor (let i = 0; i < 120; i++) {\r\n\t\t\t\t\tconst row = Math.floor(i / 12) // 当前行（0-9）\r\n\t\t\t\t\tconst col = i % 12 // 当前列（0-11）\r\n\r\n\t\t\t\t\t// 计算位置因子\r\n\t\t\t\t\tconst rowFactor = row / 9.0 // 行因子 0-1\r\n\t\t\t\t\tconst colFactor = col / 11.0 // 列因子 0-1\r\n\r\n\t\t\t\t\t// 基于选中的系列索引确定颜色系列\r\n\t\t\t\t\tlet r: number, g: number, b: number\r\n\r\n\t\t\t\t\tif (this.selectedSeriesIndex == 0) {\r\n\t\t\t\t\t\t// 随机色系列 - 每个方块完全随机的RGB值\r\n\t\t\t\t\t\tconst seed1 = (row * 12 + col + this.randomSeed) * 0.1\r\n\t\t\t\t\t\tconst seed2 = (row * 12 + col + this.randomSeed + 100) * 0.13\r\n\t\t\t\t\t\tconst seed3 = (row * 12 + col + this.randomSeed + 200) * 0.17\r\n\t\t\t\t\t\tr = Math.round((Math.sin(seed1) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tg = Math.round((Math.sin(seed2) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t\tb = Math.round((Math.sin(seed3) * 0.5 + 0.5) * 255)\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 1) {\r\n\t\t\t\t\t\t// 黑白灰系列 - 更细腻的灰度变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1的完整渐变\r\n\t\t\t\t\t\tconst grayValue = Math.round(totalFactor * 255)\r\n\t\t\t\t\t\tr = grayValue\r\n\t\t\t\t\t\tg = grayValue\r\n\t\t\t\t\t\tb = grayValue\r\n\t\t\t\t\t} else if (this.selectedSeriesIndex == 2) {\r\n\t\t\t\t\t\t// 红色系列 - 更丰富的红色变化\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\t\t\t\t\t\tconst brightness = 0.2 + totalFactor * 0.8 // 0.2-1.0的亮度范围\r\n\t\t\t\t\t\tconst saturation = 0.3 + (1 - Math.abs(totalFactor - 0.5) * 2) * 0.7 // 中间饱和度高\r\n\t\t\t\t\t\tr = Math.round(brightness * 255)\r\n\t\t\t\t\t\tg = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t\tb = Math.round(brightness * (1 - saturation) * 255)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 其他颜色系列 - 确保包含纯色且避免黑色\r\n\t\t\t\t\t\tconst totalFactor = (row * 12 + col) / 119.0 // 0到1\r\n\r\n\t\t\t\t\t\t// 根据系列索引确定基础色相\r\n\t\t\t\t\t\tlet baseHue: number\r\n\t\t\t\t\t\tif (this.selectedSeriesIndex == 3) baseHue = 30      // 橙色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 4) baseHue = 60 // 黄色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 5) baseHue = 120 // 绿色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 6) baseHue = 180 // 青色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 7) baseHue = 240 // 蓝色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 8) baseHue = 300 // 紫色\r\n\t\t\t\t\t\telse if (this.selectedSeriesIndex == 9) baseHue = 330 // 粉色\r\n\t\t\t\t\t\telse baseHue = 25 // 棕色\r\n\r\n\t\t\t\t\t\t// 色相微调：在基础色相±10度范围内变化\r\n\t\t\t\t\t\tconst hue = baseHue + (colFactor - 0.5) * 20\r\n\r\n\t\t\t\t\t\t// 创造三种类型的颜色变化\r\n\t\t\t\t\t\tif (totalFactor < 0.4) {\r\n\t\t\t\t\t\t\t// 前40%：深色调 - 高饱和度，中低明度（避免太暗）\r\n\t\t\t\t\t\t\tconst localFactor = totalFactor / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 + localFactor * 0.2 // 0.8-1.0\r\n\t\t\t\t\t\t\tconst value = 0.4 + localFactor * 0.3 // 0.4-0.7（避免太暗）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else if (totalFactor < 0.6) {\r\n\t\t\t\t\t\t\t// 中20%：纯色调 - 最高饱和度，最佳明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.4) / 0.2\r\n\t\t\t\t\t\t\tconst saturation = 1.0 // 最高饱和度\r\n\t\t\t\t\t\t\tconst value = 0.8 + localFactor * 0.2 // 0.8-1.0（确保亮度足够）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t// 后40%：浅色调 - 降低饱和度，保持高明度\r\n\t\t\t\t\t\t\tconst localFactor = (totalFactor - 0.6) / 0.4\r\n\t\t\t\t\t\t\tconst saturation = 0.8 - localFactor * 0.6 // 0.8-0.2（逐渐降低饱和度）\r\n\t\t\t\t\t\t\tconst value = 0.9 + localFactor * 0.1 // 0.9-1.0（保持高明度）\r\n\t\t\t\t\t\t\tconst rgb = this.hsvToRgb(hue, saturation, value)\r\n\t\t\t\t\t\t\tr = rgb.r\r\n\t\t\t\t\t\t\tg = rgb.g\r\n\t\t\t\t\t\t\tb = rgb.b\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 确保RGB值在0-255范围内\r\n\t\t\t\t\tr = Math.max(0, Math.min(255, r))\r\n\t\t\t\t\tg = Math.max(0, Math.min(255, g))\r\n\t\t\t\t\tb = Math.max(0, Math.min(255, b))\r\n\r\n\t\t\t\t\tcolors.push(`rgb(${r}, ${g}, ${b})`)\r\n\t\t\t\t}\r\n\r\n\t\t\t\treturn colors\r\n\t\t\t},\r\n\r\n\r\n\r\n\t\t\t// 最终的RGBA颜色值\r\n\t\t\tfinalColor() : string {\r\n\t\t\t\t// 优先使用自定义颜色\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\t// 提取RGB值并添加透明度\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tconst r = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tconst g = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tconst b = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t\treturn `rgba(${r}, ${g}, ${b}, ${this.opacity})`\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn `rgba(255, 0, 0, ${this.opacity})`\r\n\t\t\t},\r\n\r\n\t\t\t// 临时RGB颜色预览\r\n\t\t\ttempRGBColor() : string {\r\n\t\t\t\treturn `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 颜色系列选择事件\r\n\t\t\tonSeriesSelect(index: number) {\r\n\t\t\t\tthis.selectedSeriesIndex = index\r\n\t\t\t\tthis.selectedColorIndex = 0 // 重置选中的颜色\r\n\t\t\t\tthis.customColor = \"\" // 清除自定义颜色\r\n\r\n\t\t\t\t// 如果选择的是随机色系列，生成新的随机种子\r\n\t\t\t\tif (index == 0) { // 随机色现在是第1个按钮，索引为0\r\n\t\t\t\t\tthis.randomSeed = Math.floor(Math.random() * 1000)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 显示透明度选择器\r\n\t\t\tshowOpacityPicker() {\r\n\t\t\t\tconst opacityOptions = [\r\n\t\t\t\t\t'100%', '95%', '90%', '85%', '80%', '75%', '70%', '65%', '60%', '55%',\r\n\t\t\t\t\t'50%', '45%', '40%', '35%', '30%', '25%', '20%', '15%', '10%', '5%'\r\n\t\t\t\t]\r\n\r\n\t\t\t\tuni.showActionSheet({\r\n\t\t\t\t\titemList: opacityOptions,\r\n\t\t\t\t\tsuccess: (res) => {\r\n\t\t\t\t\t\tconst selectedOpacity = (100 - res.tapIndex * 5) / 100\r\n\t\t\t\t\t\tthis.opacity = selectedOpacity\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\r\n\t\t\t// 颜色选择事件\r\n\t\t\tonColorSelect(index : number) {\r\n\t\t\t\tthis.selectedColorIndex = index\r\n\t\t\t\t// 清除自定义颜色，使用新选中的颜色\r\n\t\t\t\tthis.customColor = \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 显示RGB设置弹窗\r\n\t\t\tshowRGBPicker() {\r\n\t\t\t\t// 获取当前颜色的RGB值（优先使用自定义颜色）\r\n\t\t\t\tlet colorToUse = \"\"\r\n\t\t\t\tif (this.customColor != \"\") {\r\n\t\t\t\t\tcolorToUse = this.customColor\r\n\t\t\t\t} else if (this.colorList.length > this.selectedColorIndex) {\r\n\t\t\t\t\tcolorToUse = this.colorList[this.selectedColorIndex]\r\n\t\t\t\t}\r\n\r\n\t\t\t\tif (colorToUse != \"\") {\r\n\t\t\t\t\tconst rgbMatch = colorToUse.match(/rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)/)\r\n\t\t\t\t\tif (rgbMatch != null) {\r\n\t\t\t\t\t\tthis.tempR = parseInt(rgbMatch[1] as string)\r\n\t\t\t\t\t\tthis.tempG = parseInt(rgbMatch[2] as string)\r\n\t\t\t\t\t\tthis.tempB = parseInt(rgbMatch[3] as string)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.tempR = 255\r\n\t\t\t\t\tthis.tempG = 0\r\n\t\t\t\t\tthis.tempB = 0\r\n\t\t\t\t}\r\n\t\t\t\tthis.showRGBModal = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭RGB设置弹窗\r\n\t\t\tcloseRGBPicker() {\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// RGB弹窗点击事件（阻止冒泡）\r\n\t\t\tonRGBModalClick() {\r\n\t\t\t\t// 空方法，用于阻止事件冒泡\r\n\t\t\t},\r\n\r\n\t\t\t// 确认RGB设置\r\n\t\t\tconfirmRGBPicker() {\r\n\t\t\t\t// 设置自定义颜色\r\n\t\t\t\tthis.customColor = `rgb(${this.tempR}, ${this.tempG}, ${this.tempB})`\r\n\t\t\t\tthis.showRGBModal = false\r\n\t\t\t},\r\n\r\n\t\t\t// R值滑块变化\r\n\t\t\tonTempRChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempR = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// G值滑块变化\r\n\t\t\tonTempGChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempG = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// B值滑块变化\r\n\t\t\tonTempBChange(event: UniSliderChangeEvent) {\r\n\t\t\t\tthis.tempB = event.detail.value as number\r\n\t\t\t},\r\n\r\n\t\t\t// R值输入框变化\r\n\t\t\tonTempRInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempR = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// G值输入框变化\r\n\t\t\tonTempGInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempG = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// B值输入框变化\r\n\t\t\tonTempBInput(event: UniInputEvent) {\r\n\t\t\t\tconst value = parseInt(event.detail.value)\r\n\t\t\t\tif (!isNaN(value)) {\r\n\t\t\t\t\tthis.tempB = Math.max(0, Math.min(255, value))\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 打开弹窗\r\n\t\t\topen() {\r\n\t\t\t\tthis.visible = true\r\n\t\t\t},\r\n\r\n\t\t\t// 关闭弹窗\r\n\t\t\tclose() {\r\n\t\t\t\tthis.visible = false\r\n\t\t\t},\r\n\r\n\t\t\t// 点击遮罩层关闭弹窗\r\n\t\t\tonOverlayClick() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 取消按钮点击事件\r\n\t\t\tonCancel() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tthis.$emit('cancel')\r\n\t\t\t},\r\n\r\n\t\t\t// 确定按钮点击事件\r\n\t\t\tonConfirm() {\r\n\t\t\t\tthis.close()\r\n\t\t\t\tconst rgbaValues = this.getRGBAValues()\r\n\t\t\t\tthis.$emit('confirm', {\r\n\t\t\t\t\tcolor: this.finalColor,\r\n\t\t\t\t\trgba: rgbaValues,\r\n\t\t\t\t\thex: this.rgbToHex(rgbaValues.r, rgbaValues.g, rgbaValues.b)\r\n\t\t\t\t})\r\n\t\t\t},\r\n \r\n\t\t\t// 获取RGBA数值\r\n\t\t\tgetRGBAValues() : RGBAValues {\r\n\t\t\t\tconst rgbaMatch = this.finalColor.match(/rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+),\\s*([\\d.]+)\\)/)\r\n\t\t\t\tif (rgbaMatch != null) {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tr: parseInt(rgbaMatch[1] as string),\r\n\t\t\t\t\t\tg: parseInt(rgbaMatch[2] as string),\r\n\t\t\t\t\t\tb: parseInt(rgbaMatch[3] as string),\r\n\t\t\t\t\t\ta: parseFloat(rgbaMatch[4] as string)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn { r: 255, g: 0, b: 0, a: 1.0 }\r\n\t\t\t},\r\n\r\n\t\t\t// HSV转RGB\r\n\t\t\thsvToRgb(h: number, s: number, v: number): RGBValues {\r\n\t\t\t\tconst c: number = v * s\r\n\t\t\t\tconst x: number = c * (1.0 - Math.abs(((h / 60.0) % 2.0) - 1.0))\r\n\t\t\t\tconst m: number = v - c\r\n\r\n\t\t\t\tlet r: number = 0.0\r\n\t\t\t\tlet g: number = 0.0\r\n\t\t\t\tlet b: number = 0.0\r\n\r\n\t\t\t\tif (h >= 0 && h < 60) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 60 && h < 120) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = 0.0\r\n\t\t\t\t} else if (h >= 120 && h < 180) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = c\r\n\t\t\t\t\tb = x\r\n\t\t\t\t} else if (h >= 180 && h < 240) {\r\n\t\t\t\t\tr = 0.0\r\n\t\t\t\t\tg = x\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 240 && h < 300) {\r\n\t\t\t\t\tr = x\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = c\r\n\t\t\t\t} else if (h >= 300 && h < 360) {\r\n\t\t\t\t\tr = c\r\n\t\t\t\t\tg = 0.0\r\n\t\t\t\t\tb = x\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst result: RGBValues = {\r\n\t\t\t\t\tr: Math.round((r + m) * 255.0),\r\n\t\t\t\t\tg: Math.round((g + m) * 255.0),\r\n\t\t\t\t\tb: Math.round((b + m) * 255.0)\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t},\r\n\r\n\t\t\t// RGB转十六进制\r\n\t\t\trgbToHex(r: number, g: number, b: number): string {\r\n\t\t\t\tconst toHex = (value: number): string => {\r\n\t\t\t\t\tconst hex = Math.round(Math.max(0, Math.min(255, value))).toString(16)\r\n\t\t\t\t\treturn hex.length == 1 ? '0' + hex : hex\r\n\t\t\t\t}\r\n\t\t\t\treturn '#' + toHex(r) + toHex(g) + toHex(b)\r\n\t\t\t},\r\n\r\n\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/* 弹窗遮罩层 */\r\n\t.picker-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: flex-end;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.picker-modal {\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-top-left-radius: 20rpx;\r\n\t\tborder-top-right-radius: 20rpx;\r\n\t\toverflow: hidden;\r\n\t\tbox-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);\r\n\t}\r\n\r\n\t.color-picker-container {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t\tbackground-color: #ffffff;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t/* 导航栏样式 */\r\n\t.navbar {\r\n\t\theight: 44px;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tpadding: 0 10px;\r\n\t}\r\n\r\n\t.nav-btn {\r\n\t\tfont-size: 16px;\r\n\t\tcolor: #007aff;\r\n\t\tpadding: 8px 12px;\r\n\t}\r\n\r\n\t.cancel-btn {\r\n\t\tcolor: #999999;\r\n\t}\r\n\r\n\t.confirm-btn-container {\r\n\t\theight: 30px;\r\n\t\tbackground-color: #007aff;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);\r\n\t}\r\n\r\n\t.confirm-btn {\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.nav-title {\r\n\t\tfont-size: 17px;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t/* 区域标题样式 */\r\n\t.section-title {\r\n\t\tfont-size: 14px;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 10px;\r\n\t}\r\n\r\n\t/* 颜色系列选择区域 */\r\n\t.color-series-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-series-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-between;\r\n\t\tmargin-top: 10rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t}\r\n\r\n\t.series-button {\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 2px solid transparent;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t/* 随机色按钮：两个普通按钮宽度 + 间距 */\r\n\t.random-button {\r\n\t\twidth: 220rpx;\r\n\t}\r\n\r\n\t/* 其他按钮正常宽度 */\r\n\t.normal-button {\r\n\t\twidth: 100rpx;\r\n\t}\r\n\r\n\t.series-button.active {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t.series-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t\ttext-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);\r\n\t}\r\n\r\n\t/* 颜色网格区域 */\r\n\t.color-grid-section {\r\n\t\tpadding: 20rpx;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.color-grid {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tflex-wrap: wrap;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: flex-start;\r\n\t\tpadding: 15rpx;\r\n\t}\r\n\r\n\t.color-item {\r\n\t\twidth: 55rpx;\r\n\t\theight: 40rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 2px solid transparent;\r\n\t\tmargin-bottom: 4px;\r\n\t\tflex-shrink: 0;\r\n\t\tflex-grow: 0;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.color-item.selected {\r\n\t\tborder-color: #007aff;\r\n\t\tbox-shadow: 0 0 0 1px #007aff;\r\n\t}\r\n\r\n\t/* 预览和透明度选择区域 */\r\n\t.preview-opacity-section {\r\n\t\tpadding: 15rpx 20rpx;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-bottom: 1px solid #f0f0f0;\r\n\t}\r\n\r\n\t.preview-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t.preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t\tbox-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\r\n\t}\r\n\r\n\t.rgba-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tpadding: 8rpx 12rpx;\r\n\t\tborder-radius: 6rpx;\r\n\t}\r\n\r\n\t.opacity-area {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.opacity-label {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tmargin-bottom: 8rpx;\r\n\t}\r\n\r\n\t.opacity-button {\r\n\t\tbackground-color: #007aff;\r\n\t\tpadding: 12rpx 20rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: none;\r\n\t}\r\n\r\n\t.opacity-value {\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t/* RGB设置弹窗样式 */\r\n\t.rgb-modal-overlay {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tbottom: 0;\r\n\t\tbackground-color: rgba(0, 0, 0, 0.5);\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tz-index: 1000;\r\n\t}\r\n\r\n\t.rgb-modal {\r\n\t\tbackground-color: #ffffff;\r\n\t\tborder-radius: 12rpx;\r\n\t\twidth: 600rpx;\r\n\t\tmax-height: 800rpx;\r\n\t\tpadding: 30rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-header {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-modal-title {\r\n\t\tfont-size: 32rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t}\r\n\r\n\t.rgb-preview-section {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-bottom: 25rpx;\r\n\t\tpadding: 15rpx;\r\n\t\tbackground-color: #f8f8f8;\r\n\t\tborder-radius: 8rpx;\r\n\t}\r\n\r\n\t.rgb-preview-color {\r\n\t\twidth: 60rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tmargin-right: 15rpx;\r\n\t}\r\n\r\n\t.rgb-preview-text {\r\n\t\tfont-size: 24rpx;\r\n\t\tcolor: #666666;\r\n\t\tfont-family: monospace;\r\n\t}\r\n\r\n\t.rgb-controls {\r\n\t\tmargin-bottom: 25rpx;\r\n\t}\r\n\r\n\t.rgb-control-item {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.rgb-label {\r\n\t\twidth: 40rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tcolor: #333333;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.rgb-slider {\r\n\t\tflex: 1;\r\n\t\tmargin: 0 15rpx;\r\n\t}\r\n\r\n\t.rgb-input {\r\n\t\twidth: 120rpx;\r\n\t\theight: 60rpx;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t\tborder-radius: 6rpx;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.rgb-modal-buttons {\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: row;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.rgb-button {\r\n\t\twidth: 45%;\r\n\t\theight: 70rpx;\r\n\t\tborder-radius: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.rgb-cancel {\r\n\t\tbackground-color: #f5f5f5;\r\n\t\tborder: 1px solid #e5e5e5;\r\n\t}\r\n\r\n\t.rgb-confirm {\r\n\t\tbackground-color: #007aff;\r\n\t}\r\n\r\n\t.rgb-button-text {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t}\r\n\r\n\t.rgb-cancel .rgb-button-text {\r\n\t\tcolor: #666666;\r\n\t}\r\n\r\n\t.rgb-confirm .rgb-button-text {\r\n\t\tcolor: #ffffff;\r\n\t}\r\n</style>", "<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"select-container\" @click=\"showSelector\">\n\t\t\t\t<view class=\"select-text-wrapper\">\n\t\t\t\t\t<text class=\"select-text\" :class=\"{'select-placeholder': selectedText===''}\">\n\t\t\t\t\t\t{{ displayText }}\n\t\t\t\t\t</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"select-icon-wrapper\">\n\t\t\t\t\t<text class=\"select-icon\">▼</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\ttype SelectOption = {\n\t\ttext: string;\n\t\tvalue: any;\n\t}\n\n\texport default {\n\t\tname: \"FormSelect\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: Object as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as any | null, \n\t\t\t\tisSave: false as boolean,\n\t\t\t\tsave_key: \"\" as string,\n\t\t\t\ttip: \"\" as string,\n\t\t\t\tvarType: \"string\" as string,\n\t\t\t\tselectOptions: [] as SelectOption[],\n\t\t\t\tselectedText: \"\" as string,\n\t\t\t\tplaceholder: \"请选择1\" as string,\n\t\t\t\tshowError: false as boolean,\n\t\t\t\terrorMessage: \"\" as string\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\tdisplayText(): string {\n\t\t\t\tif (this.selectedText != \"\") {\n\t\t\t\t\treturn this.selectedText\n\t\t\t\t} else {\n\t\t\t\t\treturn this.placeholder\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\twatch: {\n\t\t\tdata: { \n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateSelectedText()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name as string\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tconst configVarType = extalJson.getString(\"varType\") ?? \"string\"\n\t\t\t\t// 校验 varType 的有效性\n\t\t\t\tthis.varType = this.validateVarType(configVarType)\n\t\t\t\tthis.placeholder = extalJson.getString(\"placeholder\") ?? \"请选择\"\n\t\t\t\t\n\t\t\t\t// 解析选项数据\n\t\t\t\tconst optionsArray = extalJson.getArray(\"options\")\n\t\t\t\tif (optionsArray != null) {\n\t\t\t\t\tthis.selectOptions = []\n\t\t\t\t\tfor (let i = 0; i < optionsArray.length; i++) {\n\t\t\t\t\t\tconst optionObj = optionsArray[i] as UTSJSONObject\n\t\t\t\t\t\tconst option: SelectOption = {\n\t\t\t\t\t\t\ttext: optionObj.getString(\"text\") ?? \"\",\n\t\t\t\t\t\t\tvalue: optionObj.get(\"value\") ?? \"\"\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.selectOptions.push(option)\n\t\t\t\t\t} \n\t\t\t\t}\n\t\t\t\t// 更新选中文本\n\t\t\t\tthis.updateSelectedText()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 校验 varType 的有效性\n\t\t\tvalidateVarType(varType: string): string {\n\t\t\t\tconst validTypes = [\"string\", \"int\", \"float\"]\n\t\t\t\tif (validTypes.includes(varType)) {\n\t\t\t\t\treturn varType\n\t\t\t\t} else {\n\t\t\t\t\treturn \"string\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 更新选中文本显示\n\t\t\tupdateSelectedText(): void {\n\t\t\t\tif (this.fieldValue != null) {\n\t\t\t\t\t// 查找对应的选项文本，使用宽松比较来处理类型差异\n\t\t\t\t\tconst selectedOption = this.selectOptions.find((option: SelectOption): boolean => {\n\t\t\t\t\t\t// 使用宽松相等比较，处理数字和字符串的类型差异\n\t\t\t\t\t\treturn option.value == this.fieldValue\n\t\t\t\t\t})\n\n\t\t\t\t\tif (selectedOption != null) {\n\t\t\t\t\t\tthis.selectedText = selectedOption.text\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tthis.selectedText = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst cacheData = res.data as string\n\t\t\t\t\t\t\tlet save_value: any\n\n\t\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\t\tif (that.varType == \"int\") {\n\t\t\t\t\t\t\t\tsave_value = parseInt(cacheData)\n\t\t\t\t\t\t\t\t// 验证转换结果\n\t\t\t\t\t\t\t\tif (isNaN(save_value)) {\n\t\t\t\t\t\t\t\t\treturn // 转换失败，不使用缓存\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else if (that.varType == \"float\") {\n\t\t\t\t\t\t\t\tsave_value = parseFloat(cacheData)\n\t\t\t\t\t\t\t\t// 验证转换结果\n\t\t\t\t\t\t\t\tif (isNaN(save_value)) {\n\t\t\t\t\t\t\t\t\treturn // 转换失败，不使用缓存\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\t\t\t\tsave_value = cacheData\n\t\t\t\t\t\t\t} \n\n\t\t\t\t\t\t\tthat.fieldValue = save_value\n\t\t\t\t\t\t\tthat.updateSelectedText()\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && this.fieldValue != null) {\n\t\t\t\t\t// 统一以字符串形式存储\n\t\t\t\t\tconst cacheValue = this.fieldValue.toString()\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: cacheValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 选择器验证\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择一个选项\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value\n\t\t\t\t// 更新显示文本\n\t\t\t\tthis.updateSelectedText()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 显示选择器\n\t\t\tshowSelector(): void {\n\t\t\t\tif (this.selectOptions.length == 0) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '暂无选项',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tconst itemList = this.selectOptions.map((option: SelectOption): string => {\n\t\t\t\t\treturn option.text\n\t\t\t\t})\n\n\t\t\t\tuni.showActionSheet({\n\t\t\t\t\titemList: itemList,\n\t\t\t\t\tsuccess: (res: ShowActionSheetSuccess) => {\n\t\t\t\t\t\tconst selectedIndex = res.tapIndex\n\t\t\t\t\t\tconst selectedOption = this.selectOptions[selectedIndex] as SelectOption\n\n\t\t\t\t\t\tlet selectedValue = selectedOption.value\n\n\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\tif (this.varType == \"int\") {\n\t\t\t\t\t\t\tif (typeof selectedValue === \"number\") {\n\t\t\t\t\t\t\t\tselectedValue = Math.floor(selectedValue)\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tselectedValue = parseInt(selectedValue.toString())\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else if (this.varType == \"float\") {\n\t\t\t\t\t\t\tif (typeof selectedValue === \"number\") {\n\t\t\t\t\t\t\t\tselectedValue = selectedValue\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tselectedValue = parseFloat(selectedValue.toString())\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\t\t\tselectedValue = selectedValue.toString()\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\tvalue: selectedValue\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t},\n\t\t\t\t\tfail: () => {\n\t\t\t\t\t\tconsole.log('选择取消')\n\t\t\t\t\t}\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.select-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmin-height: 60rpx;\n\t\tpadding: 10rpx 20rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.select-text-wrapper {\n\t\tflex: 1;\n\t}\n\n\t.select-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t}\n\n\t.select-placeholder {\n\t\tcolor: #999999;\n\t}\n\n\t.select-icon-wrapper {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tdisplay: flex;\n\t\tjustify-content: center;\n\t\talign-items: center;\n\t}\n\n\t.select-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666666;\n\t}\n</style>", "import 'D:/Soft/HBuilderX/plugins/uniapp-cli-vite/node_modules/@dcloudio/uni-console/src/runtime/app/index.ts';import App from './App.uvue'\r\n\r\nimport { createSSRApp } from 'vue'\r\nexport function createApp() {\r\n\tconst app = createSSRApp(App)\r\n\treturn {\r\n\t\tapp\r\n\t}\r\n}\nexport function main(app: IApp) {\n    definePageRoutes();\n    defineAppConfig();\n    (createApp()['app'] as VueApp).mount(app, GenUniApp());\n}\n\nexport class UniAppConfig extends io.dcloud.uniapp.appframe.AppConfig {\n    override name: string = \"QitTools\"\n    override appid: string = \"__UNI__C178CB1\"\n    override versionName: string = \"1.0.0\"\n    override versionCode: string = \"100\"\n    override uniCompilerVersion: string = \"4.75\"\n    \n    constructor() { super() }\n}\n\nimport GenPagesIndexIndexClass from './pages/index/index.uvue'\nfunction definePageRoutes() {\n__uniRoutes.push({ path: \"pages/index/index\", component: GenPagesIndexIndexClass, meta: { isQuit: true } as UniPageMeta, style: _uM([[\"navigationBarTitleText\",\"uni-app x\"]]) } as UniPageRoute)\n}\nconst __uniTabBar: Map<string, any | null> | null = null\nconst __uniLaunchPage: Map<string, any | null> = _uM([[\"url\",\"pages/index/index\"],[\"style\",_uM([[\"navigationBarTitleText\",\"uni-app x\"]])]])\nfunction defineAppConfig(){\n  __uniConfig.entryPagePath = '/pages/index/index'\n  __uniConfig.globalStyle = _uM([[\"navigationBarTextStyle\",\"black\"],[\"navigationBarTitleText\",\"uni-app x\"],[\"navigationBarBackgroundColor\",\"#F8F8F8\"],[\"backgroundColor\",\"#F8F8F8\"]])\n  __uniConfig.getTabBarConfig = ():Map<string, any> | null =>  null\n  __uniConfig.tabBar = __uniConfig.getTabBarConfig()\n  __uniConfig.conditionUrl = ''\n  __uniConfig.uniIdRouter = _uM()\n  \n  __uniConfig.ready = true\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;+BAgCuB;+BCJf;+BAVA;;;;;;ADfD,IAAS,kBACd,OAAO,MAAM,EACb,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,IAAI;;IACtE,OAAO,MACJ,KAAK,CAAC,KACN,MAAM,CAAC,WAAQ,cACd,IACE,SAAS,WAAQ,cACjB,MAAM,MAAM,GACX,WAAQ,aAAsB;QAC/B,OAAO,QAAQ,IAAI,CAAC,IAAC,SAAS,WAAQ,aAAsB;YAC1D,IAAI,UAAU,IAAI;gBAAE,OAAO,WAAQ,OAAO,CAAC;;YAC3C,OAAO,iBAAiB,MAAM,MAAM;QACtC;;IACF;MACA,WAAQ,OAAO,CAAC,IAAI;AAE1B;AAEA,IAAM,yBAAiB,GAAG;AAC1B,IAAS,iBACP,MAAM,MAAM,EACZ,MAAM,MAAM,EACZ,IAAI,MAAM,GACT,WAAQ,aAAmB;IAC5B,OAAO,AAAI,WAAQ,IAAC,SAAS,OAAW;QACtC,IAAM,SAAS,uCACb,MAAK,AAAC,UAAO,OAAK,MAAG,OAAK,MAAG,IAC7B,OAAA,OAAO;YACL,QAAQ,IAAI;QACd;;QAEF,IAAM,QAAQ,WAAW,KAAM;YAE7B,OAAO,KAAK,oBACV,OAAM,IAAI,EACV,SAAQ;YAEV,QAAQ,IAAI;QACd;UAAG;QAEH,OAAO,MAAM,CAAC,IAAC,EAAM;YACnB,aAAa;YACb,QAAQ;QACV;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;QACA,OAAO,OAAO,CAAC,IAAC,EAAM;YACpB,aAAa;YACb,QAAQ,IAAI;QACd;;IACF;;AACF;AE1DO,IAAS,4BAA4B,WAAQ,OAAO,EAAE;IAC3D,IAAM,OAAO,MAAM;IACnB,IAAM,MAAM,MAAM;IAClB,IAAM,IAAI,MAAM;IAChB,IAAI,SAAS,MAAM,QAAQ,MAAM,MAAM;QAAI,OAAO,WAAQ,OAAO,CAAC,KAAK;;IACvE,IAAI,YAAY,cAAoB,IAAI;IACxC,4BACE,OAAI,MAAM,CAAI;QACZ;IACF;MACA,IAAC,MAAM,MAAM,CAAK;QAChB,YAAY,8BACV,OAAA;IAEJ;;IAEF,OAAO,WAAQ,OAAO,GACnB,IAAI,CAAC,OAAI,WAAQ,OAAO,EAAK;QAC5B,OAAO,kBAAkB,OAAO,MAAM,IAAI,IAAI,CAAC,IAAC,SAAS,OAAO,CAAI;YAClE,IAAI,UAAU,IAAI,EAAE;gBAClB,OAAO,KAAK;YACd;YACA,aAAa;YACb,OAAO,IAAI;QACb;;IACF;MACC,OAAK,CAAC,OAAI,OAAO,CAAI;QACpB,OAAO,KAAK;IACd;;AACJ;;IAEA;;AD/BC,IAAI,wBAAgB,CAAA;AAEf;;iBACM,wBAAA;YACT,QAAQ,GAAG,CAAC,cAAY;QACzB;;kBACQ,sBAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;kBACQ,MAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;4BAEqB,MAAA;YACpB,QAAQ,GAAG,CAAC,yBAAuB;YACnC,IAAI,iBAAiB,CAAC,EAAE;gBACvB,+BACC,QAAO,YACP,WAAU;gBAEX,gBAAgB,KAAK,GAAG;gBACxB,WAAW,KAAI;oBACd,gBAAgB,CAAA;gBACjB,GAAG,IAAI;mBACD,IAAI,KAAK,GAAG,KAAK,gBAAgB,IAAI,EAAE;gBAC7C,gBAAgB,KAAK,GAAG;gBACxB;;QAEF;;eAEQ,MAAA;YACP,QAAQ,GAAG,CAAC,YAAU;QACvB;;;;;;;;;;;;;;AACD;;;;;;;;AEnC2B,WAAhB;IACX;kBAAM,MAAM,CAAC;IACb;mBAAO,MAAM,CAAC;IACd;mBAAO,MAAM,CAAC;IACd;oBAAQ,GAAG,CAAC;IACZ,iBAAU,OAAO,SAAC;IAClB,oBAAa,MAAM,SAAC;IACpB;oBAAQ,cAAa;;;;;;;;;yCAPV,4BAAA;;;;;oHACX,cAAA,KACA,eAAA,MACA,eAAA,MACA,gBAAA,OACA,iBAAA,QACA,oBAAA,WACA,gBAAA;;;;;;;;;iBANA,KAAM,MAAM;;gDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,MAAO,MAAM;;iDAAb;;;;;;mCAAA;oBAAA;;;iBACA,MAAO,MAAM;;iDAAb;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ,GAAG;;kDAAX;;;;;;mCAAA;oBAAA;;;iBACA,QAAU,OAAO;;mDAAjB;;;;;;mCAAA;oBAAA;;;iBACA,WAAa,MAAM;;sDAAnB;;;;;;mCAAA;oBAAA;;;iBACA,OAAQ;;kDAAR;;;;;;mCAAA;oBAAA;;;;AAG6B,WAAlB;IACX;oBAAQ,MAAM,CAAC;IACf;oBAAQ,GAAG,CAAA;;;;;;AAKkB,WAAlB;IACV;oBAAO,MAAM,CAAC;IACd;wBAAqB;IACrB,sBAAc,OAAO,SAAA;;;;;;;;;2CAHX,8BAAA;;;;;sHACV,gBAAA,OACA,gBAAA,OACA,sBAAA;;;;;;;;;iBAFA,OAAO,MAAM;;kDAAb;;;;;;mCAAA;oBAAA;;;iBACA;;kDAAA;;;;;;mCAAA;oBAAA;;;iBACA,aAAc,OAAO;;wDAArB;;;;;;mCAAA;oBAAA;;;;;;;;;;;UC+DI,eAAe,MAAS;;;;;;;;ACiCZ,WAAZ;IACJ;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAK,CAAA;;;oCAHO,aAAA,uDAAA,GAAA,EAAA,CAAA;;;;;;qCAAZ,wBAAA;;;;;gHACJ,YAAA,GACA,YAAA,GACA,YAAA;;;;;;;;;iBAFA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAK;;8CAAT;;;;;;mCAAA;oBAAA;;;;AAEiB,WAAb;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAJQ,cAAA,uDAAA,GAAA,EAAA,CAAA;;;AAMD,WAAZ;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAHO,aAAA,uDAAA,GAAA,EAAA,CAAA;;;AAKE,WAAd;IACH;mBAAM,MAAM,CAAA;IACZ;oBAAO,MAAK,CAAA;;;oCAFK,eAAA,uDAAA,GAAA,EAAA,CAAA;;;;;;uCAAd,0BAAA;;;;;kHACH,eAAA,MACA,gBAAA;;;;;;;;;iBADA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAK;;kDAAZ;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AClBe,WAAZ;IACJ;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAM,CAAA;IACV;gBAAI,MAAK,CAAA;;;oCAHO,aAAA,qDAAA,GAAA,EAAA,CAAA;;;;;;sCAAZ,yBAAA;;;;;iHACJ,YAAA,GACA,YAAA,GACA,YAAA;;;;;;;;;iBAFA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAM;;8CAAV;;;;;;mCAAA;oBAAA;;;iBACA,GAAI,MAAK;;8CAAT;;;;;;mCAAA;oBAAA;;;;AAEiB,WAAb;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAJQ,cAAA,qDAAA,GAAA,EAAA,CAAA;;;AAMD,WAAZ;IACH;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAM,CAAA;IACT;gBAAG,MAAK,CAAA;;;oCAHO,aAAA,qDAAA,GAAA,EAAA,CAAA;;;AAKE,WAAd;IACH;mBAAM,MAAM,CAAA;IACZ;oBAAO,MAAK,CAAA;;;oCAFK,eAAA,qDAAA,GAAA,EAAA,CAAA;;;;;;wCAAd,2BAAA;;;;;mHACH,eAAA,MACA,gBAAA;;;;;;;;;iBADA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,MAAK;;kDAAZ;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;AChHkB,WAAf;IACJ;mBAAM,MAAM,CAAA;IACZ;oBAAO,GAAG,CAAA;;;oCAFS,gBAAA,oDAAA,EAAA,EAAA,CAAA;;;;;;wCAAf,2BAAA;;;;;mHACJ,eAAA,MACA,gBAAA;;;;;;;;;iBADA,MAAM,MAAM;;iDAAZ;;;;;;mCAAA;oBAAA;;;iBACA,OAAO,GAAG;;kDAAV;;;;;;mCAAA;oBAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACrBI,IAAU,aAAS,cAAA;IACxB,IAAM,MAAM;IACZ,OAAO,IACN,SAAA;AAEF;AACM,IAAU,KAAK,KAAK,IAAI,EAAA;IAC1B;IACA;IACA,CAAC,WAAW,CAAC,MAAM,CAAA,EAAA,CAAI,MAAM,EAAE,KAAK,CAAC,KAAK;AAC9C;AAEM,WAAO,eAAqB,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS;IACjE,aAAS,MAAM,MAAM,GAAG,UAAU;IAClC,aAAS,OAAO,MAAM,GAAG,gBAAgB;IACzC,aAAS,aAAa,MAAM,GAAG,OAAO;IACtC,aAAS,aAAa,MAAM,GAAG,KAAK;IACpC,aAAS,oBAAoB,MAAM,GAAG,MAAM;IAE5C,gBAAgB,KAAK,GAArB,CAAwB;;AAI5B,IAAS,mBAAgB;IACzB,YAAY,IAAI,cAAG,OAAM,qBAAqB,qCAAoC,mBAAQ,SAAQ,IAAI,GAAmB,QAAO,IAAM,4BAAyB;AAC/J;AAEA,IAAM,iBAAiB,IAAI,MAAM,EAAE,GAAG,KAAW,IAAM,SAAM,qBAAsB,WAAQ,IAAM,4BAAyB;AAC1H,IAAS,kBAAe;IACtB,YAAY,aAAa,GAAG;IAC5B,YAAY,WAAW,GAAG,IAAM,4BAAyB,SAAU,4BAAyB,aAAc,kCAA+B,WAAY,qBAAkB;IACvK,YAAY,eAAe,GAAG,OAAG,IAAI,MAAM,EAAE,GAAG;eAAa,IAAI;;IACjE,YAAY,MAAM,GAAG,YAAY,eAAe;IAChD,YAAY,YAAY,GAAG;IAC3B,YAAY,WAAW,GAAG;IAE1B,YAAY,KAAK,GAAG,IAAI;AAC1B;;;;8BAxCA,EAAA;;;;8BAAA,EAAA;;;;uBAAA,EAAA"}