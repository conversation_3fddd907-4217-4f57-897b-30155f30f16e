@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.getStorage as uni_getStorage
import io.dcloud.uniapp.extapi.setStorage as uni_setStorage
open class GenComponentsMainFormComponentsFormDatetime : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onCreated(fun(): Unit {
            val fieldObj = this.`$props`["data"] as FormFieldData
            this.initFieldData(fieldObj)
        }
        , __ins)
        this.`$watch`(fun(): Any? {
            return this.data
        }
        , fun(obj: FormFieldData) {
            val newValue = obj.value as String
            if (newValue !== this.fieldValue) {
                this.fieldValue = newValue
                this.updateDisplayValue()
            }
        }
        , WatchOptions(deep = true))
    }
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_form_container = resolveComponent("form-container")
        val _component_MainDateTimePicker = resolveComponent("MainDateTimePicker")
        return _cE(Fragment, null, _uA(
            _cV(_component_form_container, _uM("label" to _ctx.fieldName, "show-error" to _ctx.showError, "tip" to _ctx.tip, "error-message" to _ctx.errorMessage, "label-color" to _ctx.labelColor, "background-color" to _ctx.backgroundColor), _uM("input-content" to withSlotCtx(fun(): UTSArray<Any> {
                return _uA(
                    _cE("view", _uM("class" to "datetime-display-container", "onClick" to _ctx.openDateTimePicker), _uA(
                        _cE("text", _uM("class" to "datetime-text"), _tD(_ctx.displayValue), 1),
                        if (_ctx.displayValue === "") {
                            _cE("text", _uM("key" to 0, "class" to "datetime-placeholder"), "请选择时间")
                        } else {
                            _cC("v-if", true)
                        }
                    ), 8, _uA(
                        "onClick"
                    ))
                )
            }
            ), "_" to 1), 8, _uA(
                "label",
                "show-error",
                "tip",
                "error-message",
                "label-color",
                "background-color"
            )),
            _cV(_component_MainDateTimePicker, _uM("ref" to "datetimePicker", "mode" to _ctx.varType, "onConfirm" to _ctx.onDateTimeConfirm, "onCancel" to _ctx.onDateTimeCancel), null, 8, _uA(
                "mode",
                "onConfirm",
                "onCancel"
            ))
        ), 64)
    }
    open var data: Any? by `$props`
    open var index: Number by `$props`
    open var keyName: String by `$props`
    open var labelColor: String by `$props`
    open var backgroundColor: String by `$props`
    open var fieldName: String by `$data`
    open var fieldValue: String by `$data`
    open var isSave: Boolean by `$data`
    open var save_key: String by `$data`
    open var tip: String by `$data`
    open var varType: DateTimeMode1 by `$data`
    open var displayValue: String by `$data`
    open var showError: Boolean by `$data`
    open var errorMessage: String by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("fieldName" to "", "fieldValue" to "" as String, "isSave" to false, "save_key" to "", "tip" to "", "varType" to "datetime" as DateTimeMode1, "displayValue" to "", "showError" to false, "errorMessage" to "")
    }
    open var initFieldData = ::gen_initFieldData_fn
    open fun gen_initFieldData_fn(fieldObj: FormFieldData): Unit {
        val fieldKey = fieldObj.key
        val fieldValue = fieldObj.value as String
        this.fieldName = fieldObj.name
        this.fieldValue = fieldValue
        this.isSave = fieldObj.isSave ?: false
        this.save_key = this.keyName + "_" + fieldKey
        val extalJson = fieldObj.extra as UTSJSONObject
        this.tip = extalJson.getString("tip") ?: ""
        this.varType = extalJson.getString("varType") ?: "datetime" as DateTimeMode1
        this.updateDisplayValue()
        this.getCache()
    }
    open var updateDisplayValue = ::gen_updateDisplayValue_fn
    open fun gen_updateDisplayValue_fn(): Unit {
        if (this.fieldValue != "") {
            this.displayValue = this.fieldValue as String
        } else {
            this.displayValue = ""
        }
    }
    open var getCache = ::gen_getCache_fn
    open fun gen_getCache_fn(): Unit {
        if (this.isSave) {
            val that = this
            uni_getStorage(GetStorageOptions(key = this.save_key, success = fun(res: GetStorageSuccess){
                val save_value = res.data
                if (UTSAndroid.`typeof`(save_value) === "string") {
                    that.fieldValue = save_value as String
                    that.updateDisplayValue()
                    val result = FormChangeEvent(index = this.index, value = save_value)
                    this.change(result)
                }
            }
            ))
        }
    }
    open var setCache = ::gen_setCache_fn
    open fun gen_setCache_fn(): Unit {
        if (this.isSave && UTSAndroid.`typeof`(this.fieldValue) === "string") {
            uni_setStorage(SetStorageOptions(key = this.save_key, data = this.fieldValue))
        }
    }
    open var validate = ::gen_validate_fn
    open fun gen_validate_fn(): Boolean {
        if (this.fieldValue == "") {
            this.showError = true
            this.errorMessage = "请选择时间"
            return false
        }
        this.showError = false
        this.errorMessage = ""
        return true
    }
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent): Unit {
        this.fieldValue = event.value as String
        this.updateDisplayValue()
        this.setCache()
        this.`$emit`("change", event)
    }
    open var openDateTimePicker = ::gen_openDateTimePicker_fn
    open fun gen_openDateTimePicker_fn(): Unit {
        val datetimePicker = this.`$refs`["datetimePicker"] as ComponentPublicInstance
        if (this.fieldValue != "") {
            datetimePicker.`$callMethod`("show", this.fieldValue)
        } else {
            datetimePicker.`$callMethod`("show")
        }
    }
    open var onDateTimeConfirm = ::gen_onDateTimeConfirm_fn
    open fun gen_onDateTimeConfirm_fn(dateTimeData: UTSJSONObject): Unit {
        val selectedDateTime = dateTimeData.getString("formatted") ?: ""
        val result = FormChangeEvent(index = this.index, value = selectedDateTime)
        this.change(result)
    }
    open var onDateTimeCancel = ::gen_onDateTimeCancel_fn
    open fun gen_onDateTimeCancel_fn(): Unit {}
    companion object {
        var name = "FormDateTime"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("datetime-display-container" to _pS(_uM("flex" to 1, "display" to "flex", "flexDirection" to "row", "alignItems" to "center", "minHeight" to "60rpx", "paddingTop" to "10rpx", "paddingRight" to "10rpx", "paddingBottom" to "10rpx", "paddingLeft" to "10rpx", "borderTopLeftRadius" to "10rpx", "borderTopRightRadius" to "10rpx", "borderBottomRightRadius" to "10rpx", "borderBottomLeftRadius" to "10rpx", "backgroundColor" to "rgba(255,255,255,0.8)")), "datetime-text" to _pS(_uM("flex" to 1, "fontSize" to "28rpx", "color" to "#333333")), "datetime-placeholder" to _pS(_uM("flex" to 1, "fontSize" to "28rpx", "color" to "#999999")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM("change" to null)
        var props = _nP(_uM("data" to _uM(), "index" to _uM("type" to "Number", "default" to 0), "keyName" to _uM("type" to "String", "default" to ""), "labelColor" to _uM("type" to "String", "default" to "#000"), "backgroundColor" to _uM("type" to "String", "default" to "#f1f4f9")))
        var propsNeedCastKeys = _uA(
            "index",
            "keyName",
            "labelColor",
            "backgroundColor"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormContainer" to GenComponentsMainFormComponentsFormContainerClass, "MainDateTimePicker" to GenComponentsMainFormToolsMainDatetimePickerClass)
    }
}
