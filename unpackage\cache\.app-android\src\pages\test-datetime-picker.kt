@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
open class GenPagesTestDatetimePicker : BasePage {
    constructor(__ins: ComponentInternalInstance, __renderer: String?) : super(__ins, __renderer) {}
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_main_datetime_picker = resolveComponent("main-datetime-picker")
        return _cE("scroll-view", _uM("class" to "container"), _uA(
            _cE("view", _uM("class" to "page-container"), _uA(
                _cE("view", _uM("class" to "header"), _uA(
                    _cE("text", _uM("class" to "title"), "日期时间选择器测试")
                )),
                _cE("view", _uM("class" to "test-section"), _uA(
                    _cE(Fragment, null, RenderHelpers.renderList(_ctx.testCases, fun(test, index, __index, _cached): Any {
                        return _cE("view", _uM("class" to "test-item", "key" to index), _uA(
                            _cE("view", _uM("class" to "test-label"), _uA(
                                _cE("text", _uM("class" to "label-text"), _tD(test.label), 1),
                                _cE("text", _uM("class" to "mode-text"), "模式: " + _tD(test.mode), 1)
                            )),
                            _cE("view", _uM("class" to "test-value", "onClick" to fun(){
                                _ctx.showPicker(test.mode, index)
                            }
                            ), _uA(
                                _cE("text", _uM("class" to "value-text"), _tD(test.displayValue || "点击选择"), 1)
                            ), 8, _uA(
                                "onClick"
                            )),
                            if (isTrue(test.mapValue)) {
                                _cE("view", _uM("key" to 0, "class" to "map-display"), _uA(
                                    _cE("text", _uM("class" to "map-title"), "Map值:"),
                                    _cE("text", _uM("class" to "map-content"), _tD(_ctx.formatMapValue(test.mapValue)), 1)
                                ))
                            } else {
                                _cC("v-if", true)
                            }
                        ))
                    }
                    ), 128)
                ))
            )),
            _cV(_component_main_datetime_picker, _uM("ref" to "datetimePicker", "mode" to _ctx.currentMode, "title" to _ctx.currentTitle, "onConfirm" to _ctx.onDateTimeConfirm, "onCancel" to _ctx.onDateTimeCancel), null, 8, _uA(
                "mode",
                "title",
                "onConfirm",
                "onCancel"
            ))
        ))
    }
    open var currentMode: String by `$data`
    open var currentTitle: String by `$data`
    open var currentTestIndex: Number by `$data`
    open var testCases: UTSArray<TestCase> by `$data`
    @Suppress("USELESS_CAST")
    override fun data(): Map<String, Any?> {
        return _uM("currentMode" to "datetime" as String, "currentTitle" to "选择时间" as String, "currentTestIndex" to -1 as Number, "testCases" to _uA<TestCase>(TestCase(label = "时间范围", mode = "time-range", displayValue = "", mapValue = null), TestCase(label = "月份", mode = "month", displayValue = "", mapValue = null), TestCase(label = "日期", mode = "day", displayValue = "", mapValue = null), TestCase(label = "时间", mode = "time", displayValue = "", mapValue = null), TestCase(label = "时分秒", mode = "hour-minute-second", displayValue = "", mapValue = null), TestCase(label = "年份", mode = "year", displayValue = "", mapValue = null), TestCase(label = "年月", mode = "year-month", displayValue = "", mapValue = null)))
    }
    open var showPicker = ::gen_showPicker_fn
    open fun gen_showPicker_fn(mode: String, index: Number) {
        this.currentMode = mode
        this.currentTestIndex = index
        this.currentTitle = this.testCases[index].label
        val picker = this.`$refs`["datetimePicker"] as ComponentPublicInstance
        picker.`$callMethod`("show")
    }
    open var onDateTimeConfirm = ::gen_onDateTimeConfirm_fn
    open fun gen_onDateTimeConfirm_fn(result: Any) {
        console.log("选择结果:", result, " at pages/test-datetime-picker.uvue:117")
        if (this.currentTestIndex >= 0) {
            this.testCases[this.currentTestIndex].displayValue = result.formatted
            this.testCases[this.currentTestIndex].mapValue = result.value
        }
    }
    open var onDateTimeCancel = ::gen_onDateTimeCancel_fn
    open fun gen_onDateTimeCancel_fn() {
        console.log("取消选择", " at pages/test-datetime-picker.uvue:126")
    }
    open fun formatMapValue(mapValue: Any?): String {
        if (!mapValue) {
            return ""
        }
        if (UTSArray.isArray(mapValue)) {
            return (mapValue as UTSArray<Map<String, Any>>).map(fun(map): String {
                return this.formatSingleMap(map)
            }).join(" | ")
        } else {
            return this.formatSingleMap(mapValue as Map<String, Any>)
        }
    }
    open var formatSingleMap = ::gen_formatSingleMap_fn
    open fun gen_formatSingleMap_fn(map: Map<String, Any>): String {
        val parts: UTSArray<String> = _uA()
        if (map.has("year")) {
            parts.push("year:" + map.get("year"))
        }
        if (map.has("month")) {
            parts.push("month:" + map.get("month"))
        }
        if (map.has("day")) {
            parts.push("day:" + map.get("day"))
        }
        if (map.has("hour")) {
            parts.push("hour:" + map.get("hour"))
        }
        if (map.has("minute")) {
            parts.push("minute:" + map.get("minute"))
        }
        if (map.has("second")) {
            parts.push("second:" + map.get("second"))
        }
        return "{" + parts.join(", ") + "}"
    }
    companion object {
        var name = "TestDatetimePicker"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ), _uA(
                GenApp.styles
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("container" to _pS(_uM("flex" to 1, "backgroundColor" to "#f5f5f5")), "page-container" to _pS(_uM("paddingTop" to "40rpx", "paddingRight" to "40rpx", "paddingBottom" to "40rpx", "paddingLeft" to "40rpx")), "header" to _pS(_uM("marginBottom" to "60rpx", "textAlign" to "center")), "title" to _pS(_uM("fontSize" to "36rpx", "fontWeight" to "bold", "color" to "#333333")), "test-section" to _pS(_uM("display" to "flex", "flexDirection" to "column")), "test-item" to _pS(_uM("backgroundColor" to "#ffffff", "borderTopLeftRadius" to "16rpx", "borderTopRightRadius" to "16rpx", "borderBottomRightRadius" to "16rpx", "borderBottomLeftRadius" to "16rpx", "paddingTop" to "30rpx", "paddingRight" to "30rpx", "paddingBottom" to "30rpx", "paddingLeft" to "30rpx", "marginBottom" to "20rpx", "boxShadow" to "0 2rpx 8rpx rgba(0, 0, 0, 0.1)")), "test-label" to _pS(_uM("display" to "flex", "flexDirection" to "column", "marginBottom" to "20rpx")), "label-text" to _pS(_uM("fontSize" to "32rpx", "fontWeight" to "bold", "color" to "#333333", "marginBottom" to "8rpx")), "mode-text" to _pS(_uM("fontSize" to "24rpx", "color" to "#666666")), "test-value" to _pS(_uM("backgroundColor" to "#f8f9fa", "borderTopWidth" to "2rpx", "borderRightWidth" to "2rpx", "borderBottomWidth" to "2rpx", "borderLeftWidth" to "2rpx", "borderTopStyle" to "solid", "borderRightStyle" to "solid", "borderBottomStyle" to "solid", "borderLeftStyle" to "solid", "borderTopColor" to "#e9ecef", "borderRightColor" to "#e9ecef", "borderBottomColor" to "#e9ecef", "borderLeftColor" to "#e9ecef", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "paddingTop" to "24rpx", "paddingRight" to "24rpx", "paddingBottom" to "24rpx", "paddingLeft" to "24rpx", "marginBottom" to "20rpx")), "value-text" to _pS(_uM("fontSize" to "28rpx", "color" to "#495057")), "map-display" to _pS(_uM("backgroundColor" to "#e3f2fd", "borderTopLeftRadius" to "8rpx", "borderTopRightRadius" to "8rpx", "borderBottomRightRadius" to "8rpx", "borderBottomLeftRadius" to "8rpx", "paddingTop" to "20rpx", "paddingRight" to "20rpx", "paddingBottom" to "20rpx", "paddingLeft" to "20rpx")), "map-title" to _pS(_uM("fontSize" to "24rpx", "fontWeight" to "bold", "color" to "#1976d2", "marginBottom" to "8rpx")), "map-content" to _pS(_uM("fontSize" to "22rpx", "color" to "#424242", "wordBreak" to "break-all")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM())
        var propsNeedCastKeys: UTSArray<String> = _uA()
        var components: Map<String, CreateVueComponent> = _uM("MainDatetimePicker" to GenComponentsMainFormToolsMainDatetimePickerClass)
    }
}
