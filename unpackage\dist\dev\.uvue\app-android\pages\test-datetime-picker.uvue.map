{"version": 3, "sources": ["pages/test-datetime-picker.uvue"], "names": [], "mappings": "AAwCC,OAAO,kBAAiB,MAAO,wDAAuD,CAAA;AAEtF,KAAK,QAAO,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,UAAA,EAAA,iCAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IACf,KAAK,EAAE,MAAK,CAAA;IACZ,IAAI,EAAE,MAAK,CAAA;IACX,YAAY,EAAE,MAAK,CAAA;IACnB,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAA,GAAI,IAAG,CAAA;CACjC,CAAA;AAEA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,oBAAoB;IAC1B,UAAU,EAAE;QACX,kBAAiB;KACjB;IACD,IAAI;QACH,OAAO;YACN,WAAW,EAAE,UAAS,IAAK,MAAM;YACjC,YAAY,EAAE,MAAK,IAAK,MAAM;YAC9B,gBAAgB,EAAE,CAAC,CAAA,IAAK,MAAM;YAC9B,SAAS,EAAE;gBACV;oBACC,KAAK,EAAE,MAAM;oBACb,IAAI,EAAE,YAAY;oBAClB,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACb;gBACD;oBACC,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,OAAO;oBACb,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACb;gBACD;oBACC,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,KAAK;oBACX,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACb;gBACD;oBACC,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,MAAM;oBACZ,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACb;gBACD;oBACC,KAAK,EAAE,KAAK;oBACZ,IAAI,EAAE,oBAAoB;oBAC1B,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACb;gBACD;oBACC,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,MAAM;oBACZ,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACb;gBACD;oBACC,KAAK,EAAE,IAAI;oBACX,IAAI,EAAE,YAAY;oBAClB,YAAY,EAAE,EAAE;oBAChB,QAAQ,EAAE,IAAG;iBACd;aACD,IAAK,QAAQ,EAAC;SACf,CAAA;IACD,CAAC;IACD,OAAO,EAAE;QACR,UAAU,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;YACrC,IAAI,CAAC,WAAU,GAAI,IAAG,CAAA;YACtB,IAAI,CAAC,gBAAe,GAAI,KAAI,CAAA;YAC5B,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,KAAI,CAAA;YAE9C,MAAM,MAAK,GAAI,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAA,IAAK,uBAAsB,CAAA;YACrE,MAAM,CAAC,WAAW,CAAC,MAAM,CAAA,CAAA;QAC1B,CAAC;QAED,iBAAiB,CAAC,MAAM,EAAE,GAAG;YAC5B,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,EAAA,yCAAA,CAAA,CAAA;YAE3B,IAAI,IAAI,CAAC,gBAAe,IAAK,CAAC,EAAE;gBAC/B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAW,GAAI,MAAM,CAAC,SAAQ,CAAA;gBACpE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,QAAO,GAAI,MAAM,CAAC,KAAI,CAAA;aAC7D;QACD,CAAC;QAED,gBAAgB;YACf,OAAO,CAAC,GAAG,CAAC,MAAM,EAAA,yCAAA,CAAA,CAAA;QACnB,CAAC;QAED,cAAc,CAAC,QAAQ,8CAA8C,GAAG,MAAK;YAC5E,IAAI,CAAC,QAAQ;gBAAE,OAAO,EAAC,CAAA;YAEvB,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;gBAC5B,QAAO;gBACP,OAAO,CAAA,QAAQ,wBAAC,GAAG,CAAC,CAAA,GAAE,UAAE,EAAC,CAAE,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAA,CAAA;aACjE;iBAAO;gBACN,QAAO;gBACP,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,qBAAA,CAAA;aACrC;QACD,CAAC;QAED,eAAe,CAAC,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,MAAK;YAC5C,MAAM,KAAK,EAAE,MAAM,EAAC,GAAI,EAAC,CAAA;YAEzB,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;gBAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAA,CAAA;YACzD,IAAI,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAE,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAA,CAAA;YAC5D,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAE,KAAK,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAA,CAAA;YACtD,IAAI,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC;gBAAE,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAA,CAAA;YACzD,IAAI,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAAE,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAA,CAAA;YAC/D,IAAI,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAAE,KAAK,CAAC,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAA,CAAA;YAE/D,OAAO,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAE,CAAA;QAC9B,CAAA;KACD;CACD,CAAA,CAAA;;;;;;WAvJA,GAAA,CAiCc,aAAA,EAAA,GAAA,CAAA,EAjCD,KAAK,EAAC,WAAW,EAAA,CAAA,EAAA;QAE7B,GAAA,CAoBO,MAAA,EAAA,GAAA,CAAA,EApBD,KAAK,EAAC,gBAAgB,EAAA,CAAA,EAAA;YAC3B,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA,EAFD,KAAK,EAAC,QAAQ,EAAA,CAAA,EAAA;gBACnB,GAAA,CAAoC,MAAA,EAAA,GAAA,CAAA,EAA9B,KAAK,EAAC,OAAO,EAAA,CAAA,EAAC,WAAS,CAAA;;YAG9B,GAAA,CAcO,MAAA,EAAA,GAAA,CAAA,EAdD,KAAK,EAAC,cAAc,EAAA,CAAA,EAAA;gBACzB,GAAA,CAYO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAZyC,IAAA,CAAA,SAAS,EAAA,CAAzB,IAAI,EAAE,KAAK,EAAX,OAAI,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;2BAApC,GAAA,CAYO,MAAA,EAAA,GAAA,CAAA;wBAZD,KAAK,EAAC,WAAW;wBAAqC,GAAG,EAAE,KAAK;;wBACrE,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA,EAHD,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA;4BACvB,GAAA,CAAgD,MAAA,EAAA,GAAA,CAAA,EAA1C,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA,GAAA,CAAI,IAAI,CAAC,KAAK,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;4BACtC,GAAA,CAAkD,MAAA,EAAA,GAAA,CAAA,EAA5C,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,MAAI,GAAA,GAAA,CAAG,IAAI,CAAC,IAAI,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;wBAEzC,GAAA,CAEO,MAAA,EAAA,GAAA,CAAA;4BAFD,KAAK,EAAC,YAAY;4BAAE,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,UAAU,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,CAAA,CAAA,CAAA,CAAA;;4BAC3D,GAAA,CAAiE,MAAA,EAAA,GAAA,CAAA,EAA3D,KAAK,EAAC,YAAY,EAAA,CAAA,EAAA,GAAA,CAAI,IAAI,CAAC,YAAY,IAAA,MAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;+BAElC,IAAI,CAAC,QAAQ,CAAA;8BAAzB,GAAA,CAGO,MAAA,EAAA,GAAA,CAAA;;gCAHoB,KAAK,EAAC,aAAa;;gCAC7C,GAAA,CAAoC,MAAA,EAAA,GAAA,CAAA,EAA9B,KAAK,EAAC,WAAW,EAAA,CAAA,EAAC,OAAK,CAAA;gCAC7B,GAAA,CAAoE,MAAA,EAAA,GAAA,CAAA,EAA9D,KAAK,EAAC,aAAa,EAAA,CAAA,EAAA,GAAA,CAAI,IAAA,CAAA,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAA,CAAA,EAAA,CAAA,CAAA,UAAA,CAAA;;;;;;;QAO7D,GAAA,CAME,+BAAA,EAAA,GAAA,CAAA;YALD,GAAG,EAAC,gBAAgB;YACnB,IAAI,EAAE,IAAA,CAAA,WAAW;YACjB,KAAK,EAAE,IAAA,CAAA,YAAY;YACnB,SAAO,EAAE,IAAA,CAAA,iBAAiB;YAC1B,QAAM,EAAE,IAAA,CAAA,gBAAgB", "file": "pages/test-datetime-picker.uvue", "sourcesContent": ["<template>\n\n\t<scroll-view class=\"container\">\n\n\t\t<view class=\"page-container\">\n\t\t\t<view class=\"header\">\n\t\t\t\t<text class=\"title\">日期时间选择器测试</text>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"test-section\">\n\t\t\t\t<view class=\"test-item\" v-for=\"(test, index) in testCases\" :key=\"index\">\n\t\t\t\t\t<view class=\"test-label\">\n\t\t\t\t\t\t<text class=\"label-text\">{{ test.label }}</text>\n\t\t\t\t\t\t<text class=\"mode-text\">模式: {{ test.mode }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"test-value\" @click=\"showPicker(test.mode, index)\">\n\t\t\t\t\t\t<text class=\"value-text\">{{ test.displayValue || '点击选择' }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view v-if=\"test.mapValue\" class=\"map-display\">\n\t\t\t\t\t\t<text class=\"map-title\">Map值:</text>\n\t\t\t\t\t\t<text class=\"map-content\">{{ formatMapValue(test.mapValue) }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 日期时间选择器 -->\n\t\t<main-datetime-picker \n\t\t\tref=\"datetimePicker\"\n\t\t\t:mode=\"currentMode\"\n\t\t\t:title=\"currentTitle\"\n\t\t\t@confirm=\"onDateTimeConfirm\"\n\t\t\t@cancel=\"onDateTimeCancel\"\n\t\t/>\n\n\t</scroll-view>\n\n</template>\n\n<script>\n\timport MainDatetimePicker from '@/components/main-form/tools/main-datetime-picker.uvue'\n\t\n\ttype TestCase = {\n\t\tlabel: string\n\t\tmode: string\n\t\tdisplayValue: string\n\t\tmapValue: Map<string, any> | null\n\t}\n\t\n\texport default {\n\t\tname: \"TestDatetimePicker\",\n\t\tcomponents: {\n\t\t\tMainDatetimePicker\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentMode: 'datetime' as string,\n\t\t\t\tcurrentTitle: '选择时间' as string,\n\t\t\t\tcurrentTestIndex: -1 as number,\n\t\t\t\ttestCases: [\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '时间范围',\n\t\t\t\t\t\tmode: 'time-range',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '月份',\n\t\t\t\t\t\tmode: 'month',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '日期',\n\t\t\t\t\t\tmode: 'day',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '时间',\n\t\t\t\t\t\tmode: 'time',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '时分秒',\n\t\t\t\t\t\tmode: 'hour-minute-second',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '年份',\n\t\t\t\t\t\tmode: 'year',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t},\n\t\t\t\t\t{\n\t\t\t\t\t\tlabel: '年月',\n\t\t\t\t\t\tmode: 'year-month',\n\t\t\t\t\t\tdisplayValue: '',\n\t\t\t\t\t\tmapValue: null\n\t\t\t\t\t}\n\t\t\t\t] as TestCase[]\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\tshowPicker(mode: string, index: number) {\n\t\t\t\tthis.currentMode = mode\n\t\t\t\tthis.currentTestIndex = index\n\t\t\t\tthis.currentTitle = this.testCases[index].label\n\t\t\t\t\n\t\t\t\tconst picker = this.$refs['datetimePicker'] as ComponentPublicInstance\n\t\t\t\tpicker.$callMethod('show')\n\t\t\t},\n\t\t\t\n\t\t\tonDateTimeConfirm(result: any) {\n\t\t\t\tconsole.log('选择结果:', result)\n\t\t\t\t\n\t\t\t\tif (this.currentTestIndex >= 0) {\n\t\t\t\t\tthis.testCases[this.currentTestIndex].displayValue = result.formatted\n\t\t\t\t\tthis.testCases[this.currentTestIndex].mapValue = result.value\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tonDateTimeCancel() {\n\t\t\t\tconsole.log('取消选择')\n\t\t\t},\n\t\t\t\n\t\t\tformatMapValue(mapValue: Map<string, any> | Map<string, any>[] | null): string {\n\t\t\t\tif (!mapValue) return ''\n\t\t\t\t\n\t\t\t\tif (Array.isArray(mapValue)) {\n\t\t\t\t\t// 处理区间值\n\t\t\t\t\treturn mapValue.map(map => this.formatSingleMap(map)).join(' | ')\n\t\t\t\t} else {\n\t\t\t\t\t// 处理单个值\n\t\t\t\t\treturn this.formatSingleMap(mapValue)\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tformatSingleMap(map: Map<string, any>): string {\n\t\t\t\tconst parts: string[] = []\n\t\t\t\t\n\t\t\t\tif (map.has('year')) parts.push(`year:${map.get('year')}`)\n\t\t\t\tif (map.has('month')) parts.push(`month:${map.get('month')}`)\n\t\t\t\tif (map.has('day')) parts.push(`day:${map.get('day')}`)\n\t\t\t\tif (map.has('hour')) parts.push(`hour:${map.get('hour')}`)\n\t\t\t\tif (map.has('minute')) parts.push(`minute:${map.get('minute')}`)\n\t\t\t\tif (map.has('second')) parts.push(`second:${map.get('second')}`)\n\t\t\t\t\n\t\t\t\treturn `{${parts.join(', ')}}`\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.container {\n\t\tflex: 1;\n\t\tbackground-color: #f5f5f5;\n\t}\n\t\n\t.page-container {\n\t\tpadding: 40rpx;\n\t}\n\t\n\t.header {\n\t\tmargin-bottom: 60rpx;\n\t\ttext-align: center;\n\t}\n\t\n\t.title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\t\n\t.test-section {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\t\n\t.test-item {\n\t\tbackground-color: #ffffff;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tmargin-bottom: 20rpx;\n\t\tbox-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);\n\t}\n\t\n\t.test-label {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.label-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.mode-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t}\n\t\n\t.test-value {\n\t\tbackground-color: #f8f9fa;\n\t\tborder: 2rpx solid #e9ecef;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 20rpx;\n\t}\n\t\n\t.value-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #495057;\n\t}\n\t\n\t.map-display {\n\t\tbackground-color: #e3f2fd;\n\t\tborder-radius: 8rpx;\n\t\tpadding: 20rpx;\n\t}\n\t\n\t.map-title {\n\t\tfont-size: 24rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #1976d2;\n\t\tmargin-bottom: 8rpx;\n\t}\n\t\n\t.map-content {\n\t\tfont-size: 22rpx;\n\t\tcolor: #424242;\n\t\tword-break: break-all;\n\t}\n</style>\n"]}