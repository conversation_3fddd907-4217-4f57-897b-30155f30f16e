@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
import io.dcloud.uniapp.extapi.connectSocket as uni_connectSocket
import io.dcloud.uniapp.extapi.exit as uni_exit
import io.dcloud.uniapp.extapi.showToast as uni_showToast
val runBlock1 = run {
    __uniConfig.getAppStyles = fun(): Map<String, Map<String, Map<String, Any>>> {
        return GenApp.styles
    }
}
fun initRuntimeSocket(hosts: String, port: String, id: String): UTSPromise<SocketTask?> {
    if (hosts == "" || port == "" || id == "") {
        return UTSPromise.resolve(null)
    }
    return hosts.split(",").reduce<UTSPromise<SocketTask?>>(fun(promise: UTSPromise<SocketTask?>, host: String): UTSPromise<SocketTask?> {
        return promise.then(fun(socket): UTSPromise<SocketTask?> {
            if (socket != null) {
                return UTSPromise.resolve(socket)
            }
            return tryConnectSocket(host, port, id)
        }
        )
    }
    , UTSPromise.resolve(null))
}
val SOCKET_TIMEOUT: Number = 500
fun tryConnectSocket(host: String, port: String, id: String): UTSPromise<SocketTask?> {
    return UTSPromise(fun(resolve, reject){
        val socket = uni_connectSocket(ConnectSocketOptions(url = "ws://" + host + ":" + port + "/" + id, fail = fun(_) {
            resolve(null)
        }
        ))
        val timer = setTimeout(fun(){
            socket.close(CloseSocketOptions(code = 1006, reason = "connect timeout"))
            resolve(null)
        }
        , SOCKET_TIMEOUT)
        socket.onOpen(fun(e){
            clearTimeout(timer)
            resolve(socket)
        }
        )
        socket.onClose(fun(e){
            clearTimeout(timer)
            resolve(null)
        }
        )
        socket.onError(fun(e){
            clearTimeout(timer)
            resolve(null)
        }
        )
    }
    )
}
fun initRuntimeSocketService(): UTSPromise<Boolean> {
    val hosts: String = "************,*************,************,127.0.0.1"
    val port: String = "8090"
    val id: String = "app-android_MCjK3i"
    if (hosts == "" || port == "" || id == "") {
        return UTSPromise.resolve(false)
    }
    var socketTask: SocketTask? = null
    __registerWebViewUniConsole(fun(): String {
        return "!function(){\"use strict\";\"function\"==typeof SuppressedError&&SuppressedError;var e=[\"log\",\"warn\",\"error\",\"info\",\"debug\"],n=e.reduce((function(e,n){return e[n]=console[n].bind(console),e}),{}),t=null,r=new Set,o={};function i(e){if(null!=t){var n=e.map((function(e){if(\"string\"==typeof e)return e;var n=e&&\"promise\"in e&&\"reason\"in e,t=n?\"UnhandledPromiseRejection: \":\"\";if(n&&(e=e.reason),e instanceof Error&&e.stack)return e.message&&!e.stack.includes(e.message)?\"\".concat(t).concat(e.message,\"\\n\").concat(e.stack):\"\".concat(t).concat(e.stack);if(\"object\"==typeof e&&null!==e)try{return t+JSON.stringify(e)}catch(e){return t+String(e)}return t+String(e)})).filter(Boolean);n.length>0&&t(JSON.stringify(Object.assign({type:\"error\",data:n},o)))}else e.forEach((function(e){r.add(e)}))}function a(e,n){try{return{type:e,args:u(n)}}catch(e){}return{type:e,args:[]}}function u(e){return e.map((function(e){return c(e)}))}function c(e,n){if(void 0===n&&(n=0),n>=7)return{type:\"object\",value:\"[Maximum depth reached]\"};switch(typeof e){case\"string\":return{type:\"string\",value:e};case\"number\":return function(e){return{type:\"number\",value:String(e)}}(e);case\"boolean\":return function(e){return{type:\"boolean\",value:String(e)}}(e);case\"object\":try{return function(e,n){if(null===e)return{type:\"null\"};if(function(e){return e.\$&&s(e.\$)}(e))return function(e,n){return{type:\"object\",className:\"ComponentPublicInstance\",value:{properties:Object.entries(e.\$.type).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(s(e))return function(e,n){return{type:\"object\",className:\"ComponentInternalInstance\",value:{properties:Object.entries(e.type).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(function(e){return e.style&&null!=e.tagName&&null!=e.nodeName}(e))return function(e,n){return{type:\"object\",value:{properties:Object.entries(e).filter((function(e){var n=e[0];return[\"id\",\"tagName\",\"nodeName\",\"dataset\",\"offsetTop\",\"offsetLeft\",\"style\"].includes(n)})).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(function(e){return\"function\"==typeof e.getPropertyValue&&\"function\"==typeof e.setProperty&&e.\$styles}(e))return function(e,n){return{type:\"object\",value:{properties:Object.entries(e.\$styles).map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n);if(Array.isArray(e))return{type:\"object\",subType:\"array\",value:{properties:e.map((function(e,t){return function(e,n,t){var r=c(e,t);return r.name=\"\".concat(n),r}(e,t,n+1)}))}};if(e instanceof Set)return{type:\"object\",subType:\"set\",className:\"Set\",description:\"Set(\".concat(e.size,\")\"),value:{entries:Array.from(e).map((function(e){return function(e,n){return{value:c(e,n)}}(e,n+1)}))}};if(e instanceof Map)return{type:\"object\",subType:\"map\",className:\"Map\",description:\"Map(\".concat(e.size,\")\"),value:{entries:Array.from(e.entries()).map((function(e){return function(e,n){return{key:c(e[0],n),value:c(e[1],n)}}(e,n+1)}))}};if(e instanceof Promise)return{type:\"object\",subType:\"promise\",value:{properties:[]}};if(e instanceof RegExp)return{type:\"object\",subType:\"regexp\",value:String(e),className:\"Regexp\"};if(e instanceof Date)return{type:\"object\",subType:\"date\",value:String(e),className:\"Date\"};if(e instanceof Error)return{type:\"object\",subType:\"error\",value:e.message||String(e),className:e.name||\"Error\"};var t=void 0,r=e.constructor;r&&r.get\$UTSMetadata\$&&(t=r.get\$UTSMetadata\$().name);var o=Object.entries(e);(function(e){return e.modifier&&e.modifier._attribute&&e.nodeContent})(e)&&(o=o.filter((function(e){var n=e[0];return\"modifier\"!==n&&\"nodeContent\"!==n})));return{type:\"object\",className:t,value:{properties:o.map((function(e){return f(e[0],e[1],n+1)}))}}}(e,n)}catch(e){return{type:\"object\",value:{properties:[]}}}case\"undefined\":return{type:\"undefined\"};case\"function\":return function(e){return{type:\"function\",value:\"function \".concat(e.name,\"() {}\")}}(e);case\"symbol\":return function(e){return{type:\"symbol\",value:e.description}}(e);case\"bigint\":return function(e){return{type:\"bigint\",value:String(e)}}(e)}}function s(e){return e.type&&null!=e.uid&&e.appContext}function f(e,n,t){var r=c(n,t);return r.name=e,r}var l=null,p=[],y={},g=\"---BEGIN:EXCEPTION---\",d=\"---END:EXCEPTION---\";function v(e){null!=l?l(JSON.stringify(Object.assign({type:\"console\",data:e},y))):p.push.apply(p,e)}var m=/^\\s*at\\s+[\\w/./-]+:\\d+\$/;function b(){function t(e){return function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var o=function(e,n,t){if(t||2===arguments.length)for(var r,o=0,i=n.length;o<i;o++)!r&&o in n||(r||(r=Array.prototype.slice.call(n,0,o)),r[o]=n[o]);return e.concat(r||Array.prototype.slice.call(n))}([],t,!0);if(o.length){var u=o[o.length-1];\"string\"==typeof u&&m.test(u)&&o.pop()}if(n[e].apply(n,o),\"error\"===e&&1===t.length){var c=t[0];if(\"string\"==typeof c&&c.startsWith(g)){var s=g.length,f=c.length-d.length;return void i([c.slice(s,f)])}if(c instanceof Error)return void i([c])}v([a(e,t)])}}return function(){var e=console.log,n=Symbol();try{console.log=n}catch(e){return!1}var t=console.log===n;return console.log=e,t}()?(e.forEach((function(e){console[e]=t(e)})),function(){e.forEach((function(e){console[e]=n[e]}))}):function(){}}function _(e){var n={type:\"WEB_INVOKE_APPSERVICE\",args:{data:{name:\"console\",arg:e}}};return window.__uniapp_x_postMessageToService?window.__uniapp_x_postMessageToService(n):window.__uniapp_x_.postMessageToService(JSON.stringify(n))}!function(){if(!window.__UNI_CONSOLE_WEBVIEW__){window.__UNI_CONSOLE_WEBVIEW__=!0;var e=\"[web-view]\".concat(window.__UNI_PAGE_ROUTE__?\"[\".concat(window.__UNI_PAGE_ROUTE__,\"]\"):\"\");b(),function(e,n){if(void 0===n&&(n={}),l=e,Object.assign(y,n),null!=e&&p.length>0){var t=p.slice();p.length=0,v(t)}}((function(e){_(e)}),{channel:e}),function(e,n){if(void 0===n&&(n={}),t=e,Object.assign(o,n),null!=e&&r.size>0){var a=Array.from(r);r.clear(),i(a)}}((function(e){_(e)}),{channel:e}),window.addEventListener(\"error\",(function(e){i([e.error])})),window.addEventListener(\"unhandledrejection\",(function(e){i([e])}))}}()}();"
    }
    , fun(data: String){
        socketTask?.send(SendSocketMessageOptions(data = data))
    }
    )
    return UTSPromise.resolve().then(fun(): UTSPromise<Boolean> {
        return initRuntimeSocket(hosts, port, id).then(fun(socket): Boolean {
            if (socket == null) {
                return false
            }
            socketTask = socket
            return true
        }
        )
    }
    ).`catch`(fun(): Boolean {
        return false
    }
    )
}
val runBlock2 = run {
    initRuntimeSocketService()
}
var firstBackTime: Number = 0
open class GenApp : BaseApp {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {
        onLaunch(fun(_: OnLaunchOptions) {
            console.log("App Launch", " at App.uvue:7")
        }
        , __ins)
        onAppShow(fun(_: OnShowOptions) {
            console.log("App Show", " at App.uvue:10")
        }
        , __ins)
        onAppHide(fun() {
            console.log("App Hide", " at App.uvue:13")
        }
        , __ins)
        onLastPageBackPress(fun() {
            console.log("App LastPageBackPress", " at App.uvue:17")
            if (firstBackTime == 0) {
                uni_showToast(ShowToastOptions(title = "再按一次退出应用", position = "bottom"))
                firstBackTime = Date.now()
                setTimeout(fun(){
                    firstBackTime = 0
                }, 2000)
            } else if (Date.now() - firstBackTime < 2000) {
                firstBackTime = Date.now()
                uni_exit(null)
            }
        }
        , __ins)
        onExit(fun() {
            console.log("App Exit", " at App.uvue:34")
        }
        , __ins)
    }
    companion object {
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("uni-row" to _pS(_uM("flexDirection" to "row")), "uni-column" to _pS(_uM("flexDirection" to "column")))
            }
    }
}
val GenAppClass = CreateVueAppComponent(GenApp::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "app", name = "", inheritAttrs = true, inject = Map(), props = Map(), propsNeedCastKeys = _uA(), emits = Map(), components = Map(), styles = GenApp.styles)
}
, fun(instance): GenApp {
    return GenApp(instance)
}
)
open class FormFieldData (
    @JsonNotNull
    open var key: String,
    @JsonNotNull
    open var name: String,
    @JsonNotNull
    open var type: String,
    @JsonNotNull
    open var value: Any,
    open var isSave: Boolean? = null,
    open var condition: String? = null,
    @JsonNotNull
    open var extra: UTSJSONObject,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormFieldData", "components/main-form/form_type.uts", 1, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return FormFieldDataReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class FormFieldDataReactiveObject : FormFieldData, IUTSReactive<FormFieldData> {
    override var __v_raw: FormFieldData
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: FormFieldData, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(key = __v_raw.key, name = __v_raw.name, type = __v_raw.type, value = __v_raw.value, isSave = __v_raw.isSave, condition = __v_raw.condition, extra = __v_raw.extra) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): FormFieldDataReactiveObject {
        return FormFieldDataReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var key: String
        get() {
            return _tRG(__v_raw, "key", __v_raw.key, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("key")) {
                return
            }
            val oldValue = __v_raw.key
            __v_raw.key = value
            _tRS(__v_raw, "key", oldValue, value)
        }
    override var name: String
        get() {
            return _tRG(__v_raw, "name", __v_raw.name, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            _tRS(__v_raw, "name", oldValue, value)
        }
    override var type: String
        get() {
            return _tRG(__v_raw, "type", __v_raw.type, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("type")) {
                return
            }
            val oldValue = __v_raw.type
            __v_raw.type = value
            _tRS(__v_raw, "type", oldValue, value)
        }
    override var value: Any
        get() {
            return _tRG(__v_raw, "value", __v_raw.value, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("value")) {
                return
            }
            val oldValue = __v_raw.value
            __v_raw.value = value
            _tRS(__v_raw, "value", oldValue, value)
        }
    override var isSave: Boolean?
        get() {
            return _tRG(__v_raw, "isSave", __v_raw.isSave, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("isSave")) {
                return
            }
            val oldValue = __v_raw.isSave
            __v_raw.isSave = value
            _tRS(__v_raw, "isSave", oldValue, value)
        }
    override var condition: String?
        get() {
            return _tRG(__v_raw, "condition", __v_raw.condition, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("condition")) {
                return
            }
            val oldValue = __v_raw.condition
            __v_raw.condition = value
            _tRS(__v_raw, "condition", oldValue, value)
        }
    override var extra: UTSJSONObject
        get() {
            return _tRG(__v_raw, "extra", __v_raw.extra, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("extra")) {
                return
            }
            val oldValue = __v_raw.extra
            __v_raw.extra = value
            _tRS(__v_raw, "extra", oldValue, value)
        }
}
open class FormChangeEvent (
    @JsonNotNull
    open var index: Number,
    @JsonNotNull
    open var value: Any,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("FormChangeEvent", "components/main-form/form_type.uts", 10, 13)
    }
}
open class DateQuickOption (
    @JsonNotNull
    open var label: String,
    @JsonNotNull
    open var value: Any,
    open var autoConfirm: Boolean? = null,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("DateQuickOption", "components/main-form/form_type.uts", 14, 13)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return DateQuickOptionReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class DateQuickOptionReactiveObject : DateQuickOption, IUTSReactive<DateQuickOption> {
    override var __v_raw: DateQuickOption
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: DateQuickOption, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(label = __v_raw.label, value = __v_raw.value, autoConfirm = __v_raw.autoConfirm) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): DateQuickOptionReactiveObject {
        return DateQuickOptionReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var label: String
        get() {
            return _tRG(__v_raw, "label", __v_raw.label, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("label")) {
                return
            }
            val oldValue = __v_raw.label
            __v_raw.label = value
            _tRS(__v_raw, "label", oldValue, value)
        }
    override var value: Any
        get() {
            return _tRG(__v_raw, "value", __v_raw.value, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("value")) {
                return
            }
            val oldValue = __v_raw.value
            __v_raw.value = value
            _tRS(__v_raw, "value", oldValue, value)
        }
    override var autoConfirm: Boolean?
        get() {
            return _tRG(__v_raw, "autoConfirm", __v_raw.autoConfirm, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("autoConfirm")) {
                return
            }
            val oldValue = __v_raw.autoConfirm
            __v_raw.autoConfirm = value
            _tRS(__v_raw, "autoConfirm", oldValue, value)
        }
}
val GenComponentsMainFormToolsMainYearmonthPickerClass = CreateVueComponent(GenComponentsMainFormToolsMainYearmonthPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormToolsMainYearmonthPicker.name, inheritAttrs = GenComponentsMainFormToolsMainYearmonthPicker.inheritAttrs, inject = GenComponentsMainFormToolsMainYearmonthPicker.inject, props = GenComponentsMainFormToolsMainYearmonthPicker.props, propsNeedCastKeys = GenComponentsMainFormToolsMainYearmonthPicker.propsNeedCastKeys, emits = GenComponentsMainFormToolsMainYearmonthPicker.emits, components = GenComponentsMainFormToolsMainYearmonthPicker.components, styles = GenComponentsMainFormToolsMainYearmonthPicker.styles)
}
, fun(instance, renderer): GenComponentsMainFormToolsMainYearmonthPicker {
    return GenComponentsMainFormToolsMainYearmonthPicker(instance)
}
)
typealias DateTimeMode = String
val GenComponentsMainFormToolsMainDatetimePickerClass = CreateVueComponent(GenComponentsMainFormToolsMainDatetimePicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormToolsMainDatetimePicker.name, inheritAttrs = GenComponentsMainFormToolsMainDatetimePicker.inheritAttrs, inject = GenComponentsMainFormToolsMainDatetimePicker.inject, props = GenComponentsMainFormToolsMainDatetimePicker.props, propsNeedCastKeys = GenComponentsMainFormToolsMainDatetimePicker.propsNeedCastKeys, emits = GenComponentsMainFormToolsMainDatetimePicker.emits, components = GenComponentsMainFormToolsMainDatetimePicker.components, styles = GenComponentsMainFormToolsMainDatetimePicker.styles)
}
, fun(instance, renderer): GenComponentsMainFormToolsMainDatetimePicker {
    return GenComponentsMainFormToolsMainDatetimePicker(instance)
}
)
open class ColorInfo (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorInfo", "components/main-color-picker/main-color-picker.uvue", 117, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorInfoReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorInfoReactiveObject : ColorInfo, IUTSReactive<ColorInfo> {
    override var __v_raw: ColorInfo
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorInfo, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(r = __v_raw.r, g = __v_raw.g, b = __v_raw.b) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorInfoReactiveObject {
        return ColorInfoReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var r: Number
        get() {
            return _tRG(__v_raw, "r", __v_raw.r, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("r")) {
                return
            }
            val oldValue = __v_raw.r
            __v_raw.r = value
            _tRS(__v_raw, "r", oldValue, value)
        }
    override var g: Number
        get() {
            return _tRG(__v_raw, "g", __v_raw.g, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("g")) {
                return
            }
            val oldValue = __v_raw.g
            __v_raw.g = value
            _tRS(__v_raw, "g", oldValue, value)
        }
    override var b: Number
        get() {
            return _tRG(__v_raw, "b", __v_raw.b, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("b")) {
                return
            }
            val oldValue = __v_raw.b
            __v_raw.b = value
            _tRS(__v_raw, "b", oldValue, value)
        }
}
open class RGBAValues (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
    @JsonNotNull
    open var a: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBAValues", "components/main-color-picker/main-color-picker.uvue", 122, 7)
    }
}
open class RGBValues (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBValues", "components/main-color-picker/main-color-picker.uvue", 128, 7)
    }
}
open class ColorSeries (
    @JsonNotNull
    open var name: String,
    @JsonNotNull
    open var color: String,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorSeries", "components/main-color-picker/main-color-picker.uvue", 133, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorSeriesReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorSeriesReactiveObject : ColorSeries, IUTSReactive<ColorSeries> {
    override var __v_raw: ColorSeries
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorSeries, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(name = __v_raw.name, color = __v_raw.color) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorSeriesReactiveObject {
        return ColorSeriesReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var name: String
        get() {
            return _tRG(__v_raw, "name", __v_raw.name, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            _tRS(__v_raw, "name", oldValue, value)
        }
    override var color: String
        get() {
            return _tRG(__v_raw, "color", __v_raw.color, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("color")) {
                return
            }
            val oldValue = __v_raw.color
            __v_raw.color = value
            _tRS(__v_raw, "color", oldValue, value)
        }
}
val GenComponentsMainColorPickerMainColorPickerClass = CreateVueComponent(GenComponentsMainColorPickerMainColorPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainColorPickerMainColorPicker.name, inheritAttrs = GenComponentsMainColorPickerMainColorPicker.inheritAttrs, inject = GenComponentsMainColorPickerMainColorPicker.inject, props = GenComponentsMainColorPickerMainColorPicker.props, propsNeedCastKeys = GenComponentsMainColorPickerMainColorPicker.propsNeedCastKeys, emits = GenComponentsMainColorPickerMainColorPicker.emits, components = GenComponentsMainColorPickerMainColorPicker.components, styles = GenComponentsMainColorPickerMainColorPicker.styles)
}
, fun(instance, renderer): GenComponentsMainColorPickerMainColorPicker {
    return GenComponentsMainColorPickerMainColorPicker(instance)
}
)
val GenComponentsMainFormComponentsFormContainerClass = CreateVueComponent(GenComponentsMainFormComponentsFormContainer::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormContainer.name, inheritAttrs = GenComponentsMainFormComponentsFormContainer.inheritAttrs, inject = GenComponentsMainFormComponentsFormContainer.inject, props = GenComponentsMainFormComponentsFormContainer.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormContainer.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormContainer.emits, components = GenComponentsMainFormComponentsFormContainer.components, styles = GenComponentsMainFormComponentsFormContainer.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormContainer {
    return GenComponentsMainFormComponentsFormContainer(instance)
}
)
val GenComponentsMainFormComponentsFormInputClass = CreateVueComponent(GenComponentsMainFormComponentsFormInput::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormInput.name, inheritAttrs = GenComponentsMainFormComponentsFormInput.inheritAttrs, inject = GenComponentsMainFormComponentsFormInput.inject, props = GenComponentsMainFormComponentsFormInput.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormInput.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormInput.emits, components = GenComponentsMainFormComponentsFormInput.components, styles = GenComponentsMainFormComponentsFormInput.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormInput {
    return GenComponentsMainFormComponentsFormInput(instance)
}
)
val GenComponentsMainFormComponentsFormTextareaClass = CreateVueComponent(GenComponentsMainFormComponentsFormTextarea::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormTextarea.name, inheritAttrs = GenComponentsMainFormComponentsFormTextarea.inheritAttrs, inject = GenComponentsMainFormComponentsFormTextarea.inject, props = GenComponentsMainFormComponentsFormTextarea.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormTextarea.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormTextarea.emits, components = GenComponentsMainFormComponentsFormTextarea.components, styles = GenComponentsMainFormComponentsFormTextarea.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormTextarea {
    return GenComponentsMainFormComponentsFormTextarea(instance)
}
)
val GenComponentsMainFormComponentsFormSwitchClass = CreateVueComponent(GenComponentsMainFormComponentsFormSwitch::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormSwitch.name, inheritAttrs = GenComponentsMainFormComponentsFormSwitch.inheritAttrs, inject = GenComponentsMainFormComponentsFormSwitch.inject, props = GenComponentsMainFormComponentsFormSwitch.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormSwitch.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormSwitch.emits, components = GenComponentsMainFormComponentsFormSwitch.components, styles = GenComponentsMainFormComponentsFormSwitch.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormSwitch {
    return GenComponentsMainFormComponentsFormSwitch(instance)
}
)
val GenComponentsMainFormComponentsFormSliderClass = CreateVueComponent(GenComponentsMainFormComponentsFormSlider::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormSlider.name, inheritAttrs = GenComponentsMainFormComponentsFormSlider.inheritAttrs, inject = GenComponentsMainFormComponentsFormSlider.inject, props = GenComponentsMainFormComponentsFormSlider.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormSlider.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormSlider.emits, components = GenComponentsMainFormComponentsFormSlider.components, styles = GenComponentsMainFormComponentsFormSlider.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormSlider {
    return GenComponentsMainFormComponentsFormSlider(instance)
}
)
val GenComponentsMainFormComponentsFormNumberboxClass = CreateVueComponent(GenComponentsMainFormComponentsFormNumberbox::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormNumberbox.name, inheritAttrs = GenComponentsMainFormComponentsFormNumberbox.inheritAttrs, inject = GenComponentsMainFormComponentsFormNumberbox.inject, props = GenComponentsMainFormComponentsFormNumberbox.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormNumberbox.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormNumberbox.emits, components = GenComponentsMainFormComponentsFormNumberbox.components, styles = GenComponentsMainFormComponentsFormNumberbox.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormNumberbox {
    return GenComponentsMainFormComponentsFormNumberbox(instance)
}
)
open class ColorInfo1 (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorInfo", "components/main-form/tools/main-color-picker.uvue", 117, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorInfo1ReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorInfo1ReactiveObject : ColorInfo1, IUTSReactive<ColorInfo1> {
    override var __v_raw: ColorInfo1
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorInfo1, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(r = __v_raw.r, g = __v_raw.g, b = __v_raw.b) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorInfo1ReactiveObject {
        return ColorInfo1ReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var r: Number
        get() {
            return _tRG(__v_raw, "r", __v_raw.r, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("r")) {
                return
            }
            val oldValue = __v_raw.r
            __v_raw.r = value
            _tRS(__v_raw, "r", oldValue, value)
        }
    override var g: Number
        get() {
            return _tRG(__v_raw, "g", __v_raw.g, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("g")) {
                return
            }
            val oldValue = __v_raw.g
            __v_raw.g = value
            _tRS(__v_raw, "g", oldValue, value)
        }
    override var b: Number
        get() {
            return _tRG(__v_raw, "b", __v_raw.b, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("b")) {
                return
            }
            val oldValue = __v_raw.b
            __v_raw.b = value
            _tRS(__v_raw, "b", oldValue, value)
        }
}
open class RGBAValues1 (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
    @JsonNotNull
    open var a: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBAValues", "components/main-form/tools/main-color-picker.uvue", 122, 7)
    }
}
open class RGBValues1 (
    @JsonNotNull
    open var r: Number,
    @JsonNotNull
    open var g: Number,
    @JsonNotNull
    open var b: Number,
) : UTSObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("RGBValues", "components/main-form/tools/main-color-picker.uvue", 128, 7)
    }
}
open class ColorSeries1 (
    @JsonNotNull
    open var name: String,
    @JsonNotNull
    open var color: String,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("ColorSeries", "components/main-form/tools/main-color-picker.uvue", 133, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return ColorSeries1ReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class ColorSeries1ReactiveObject : ColorSeries1, IUTSReactive<ColorSeries1> {
    override var __v_raw: ColorSeries1
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: ColorSeries1, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(name = __v_raw.name, color = __v_raw.color) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): ColorSeries1ReactiveObject {
        return ColorSeries1ReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var name: String
        get() {
            return _tRG(__v_raw, "name", __v_raw.name, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("name")) {
                return
            }
            val oldValue = __v_raw.name
            __v_raw.name = value
            _tRS(__v_raw, "name", oldValue, value)
        }
    override var color: String
        get() {
            return _tRG(__v_raw, "color", __v_raw.color, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("color")) {
                return
            }
            val oldValue = __v_raw.color
            __v_raw.color = value
            _tRS(__v_raw, "color", oldValue, value)
        }
}
val GenComponentsMainFormToolsMainColorPickerClass = CreateVueComponent(GenComponentsMainFormToolsMainColorPicker::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormToolsMainColorPicker.name, inheritAttrs = GenComponentsMainFormToolsMainColorPicker.inheritAttrs, inject = GenComponentsMainFormToolsMainColorPicker.inject, props = GenComponentsMainFormToolsMainColorPicker.props, propsNeedCastKeys = GenComponentsMainFormToolsMainColorPicker.propsNeedCastKeys, emits = GenComponentsMainFormToolsMainColorPicker.emits, components = GenComponentsMainFormToolsMainColorPicker.components, styles = GenComponentsMainFormToolsMainColorPicker.styles)
}
, fun(instance, renderer): GenComponentsMainFormToolsMainColorPicker {
    return GenComponentsMainFormToolsMainColorPicker(instance)
}
)
val GenComponentsMainFormComponentsFormColorClass = CreateVueComponent(GenComponentsMainFormComponentsFormColor::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormColor.name, inheritAttrs = GenComponentsMainFormComponentsFormColor.inheritAttrs, inject = GenComponentsMainFormComponentsFormColor.inject, props = GenComponentsMainFormComponentsFormColor.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormColor.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormColor.emits, components = GenComponentsMainFormComponentsFormColor.components, styles = GenComponentsMainFormComponentsFormColor.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormColor {
    return GenComponentsMainFormComponentsFormColor(instance)
}
)
open class SelectOption (
    @JsonNotNull
    open var text: String,
    @JsonNotNull
    open var value: Any,
) : UTSReactiveObject(), IUTSSourceMap {
    override fun `__$getOriginalPosition`(): UTSSourceMapPosition? {
        return UTSSourceMapPosition("SelectOption", "components/main-form/components/form-select.uvue", 23, 7)
    }
    override fun __v_create(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): UTSReactiveObject {
        return SelectOptionReactiveObject(this, __v_isReadonly, __v_isShallow, __v_skip)
    }
}
open class SelectOptionReactiveObject : SelectOption, IUTSReactive<SelectOption> {
    override var __v_raw: SelectOption
    override var __v_isReadonly: Boolean
    override var __v_isShallow: Boolean
    override var __v_skip: Boolean
    constructor(__v_raw: SelectOption, __v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean) : super(text = __v_raw.text, value = __v_raw.value) {
        this.__v_raw = __v_raw
        this.__v_isReadonly = __v_isReadonly
        this.__v_isShallow = __v_isShallow
        this.__v_skip = __v_skip
    }
    override fun __v_clone(__v_isReadonly: Boolean, __v_isShallow: Boolean, __v_skip: Boolean): SelectOptionReactiveObject {
        return SelectOptionReactiveObject(this.__v_raw, __v_isReadonly, __v_isShallow, __v_skip)
    }
    override var text: String
        get() {
            return _tRG(__v_raw, "text", __v_raw.text, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("text")) {
                return
            }
            val oldValue = __v_raw.text
            __v_raw.text = value
            _tRS(__v_raw, "text", oldValue, value)
        }
    override var value: Any
        get() {
            return _tRG(__v_raw, "value", __v_raw.value, __v_isReadonly, __v_isShallow)
        }
        set(value) {
            if (!__v_canSet("value")) {
                return
            }
            val oldValue = __v_raw.value
            __v_raw.value = value
            _tRS(__v_raw, "value", oldValue, value)
        }
}
val GenComponentsMainFormComponentsFormSelectClass = CreateVueComponent(GenComponentsMainFormComponentsFormSelect::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormSelect.name, inheritAttrs = GenComponentsMainFormComponentsFormSelect.inheritAttrs, inject = GenComponentsMainFormComponentsFormSelect.inject, props = GenComponentsMainFormComponentsFormSelect.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormSelect.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormSelect.emits, components = GenComponentsMainFormComponentsFormSelect.components, styles = GenComponentsMainFormComponentsFormSelect.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormSelect {
    return GenComponentsMainFormComponentsFormSelect(instance)
}
)
val GenComponentsMainFormComponentsFormYearmonthClass = CreateVueComponent(GenComponentsMainFormComponentsFormYearmonth::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormYearmonth.name, inheritAttrs = GenComponentsMainFormComponentsFormYearmonth.inheritAttrs, inject = GenComponentsMainFormComponentsFormYearmonth.inject, props = GenComponentsMainFormComponentsFormYearmonth.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormYearmonth.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormYearmonth.emits, components = GenComponentsMainFormComponentsFormYearmonth.components, styles = GenComponentsMainFormComponentsFormYearmonth.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormYearmonth {
    return GenComponentsMainFormComponentsFormYearmonth(instance)
}
)
typealias DateTimeMode1 = String
val GenComponentsMainFormComponentsFormDatetimeClass = CreateVueComponent(GenComponentsMainFormComponentsFormDatetime::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormComponentsFormDatetime.name, inheritAttrs = GenComponentsMainFormComponentsFormDatetime.inheritAttrs, inject = GenComponentsMainFormComponentsFormDatetime.inject, props = GenComponentsMainFormComponentsFormDatetime.props, propsNeedCastKeys = GenComponentsMainFormComponentsFormDatetime.propsNeedCastKeys, emits = GenComponentsMainFormComponentsFormDatetime.emits, components = GenComponentsMainFormComponentsFormDatetime.components, styles = GenComponentsMainFormComponentsFormDatetime.styles)
}
, fun(instance, renderer): GenComponentsMainFormComponentsFormDatetime {
    return GenComponentsMainFormComponentsFormDatetime(instance)
}
)
val GenComponentsMainFormMainFormClass = CreateVueComponent(GenComponentsMainFormMainForm::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "component", name = GenComponentsMainFormMainForm.name, inheritAttrs = GenComponentsMainFormMainForm.inheritAttrs, inject = GenComponentsMainFormMainForm.inject, props = GenComponentsMainFormMainForm.props, propsNeedCastKeys = GenComponentsMainFormMainForm.propsNeedCastKeys, emits = GenComponentsMainFormMainForm.emits, components = GenComponentsMainFormMainForm.components, styles = GenComponentsMainFormMainForm.styles)
}
, fun(instance, renderer): GenComponentsMainFormMainForm {
    return GenComponentsMainFormMainForm(instance)
}
)
val GenPagesIndexIndexClass = CreateVueComponent(GenPagesIndexIndex::class.java, fun(): VueComponentOptions {
    return VueComponentOptions(type = "page", name = "", inheritAttrs = GenPagesIndexIndex.inheritAttrs, inject = GenPagesIndexIndex.inject, props = GenPagesIndexIndex.props, propsNeedCastKeys = GenPagesIndexIndex.propsNeedCastKeys, emits = GenPagesIndexIndex.emits, components = GenPagesIndexIndex.components, styles = GenPagesIndexIndex.styles)
}
, fun(instance, renderer): GenPagesIndexIndex {
    return GenPagesIndexIndex(instance, renderer)
}
)
fun createApp(): UTSJSONObject {
    val app = createSSRApp(GenAppClass)
    return _uO("app" to app)
}
fun main(app: IApp) {
    definePageRoutes()
    defineAppConfig()
    (createApp()["app"] as VueApp).mount(app, GenUniApp())
}
open class UniAppConfig : io.dcloud.uniapp.appframe.AppConfig {
    override var name: String = "QitTools"
    override var appid: String = "__UNI__C178CB1"
    override var versionName: String = "1.0.0"
    override var versionCode: String = "100"
    override var uniCompilerVersion: String = "4.75"
    constructor() : super() {}
}
fun definePageRoutes() {
    __uniRoutes.push(UniPageRoute(path = "pages/index/index", component = GenPagesIndexIndexClass, meta = UniPageMeta(isQuit = true), style = _uM("navigationBarTitleText" to "uni-app x")))
}
val __uniLaunchPage: Map<String, Any?> = _uM("url" to "pages/index/index", "style" to _uM("navigationBarTitleText" to "uni-app x"))
fun defineAppConfig() {
    __uniConfig.entryPagePath = "/pages/index/index"
    __uniConfig.globalStyle = _uM("navigationBarTextStyle" to "black", "navigationBarTitleText" to "uni-app x", "navigationBarBackgroundColor" to "#F8F8F8", "backgroundColor" to "#F8F8F8")
    __uniConfig.getTabBarConfig = fun(): Map<String, Any>? {
        return null
    }
    __uniConfig.tabBar = __uniConfig.getTabBarConfig()
    __uniConfig.conditionUrl = ""
    __uniConfig.uniIdRouter = _uM()
    __uniConfig.ready = true
}
open class GenUniApp : UniAppImpl() {
    open val vm: GenApp?
        get() {
            return getAppVm() as GenApp?
        }
    open val `$vm`: GenApp?
        get() {
            return getAppVm() as GenApp?
        }
}
fun getApp(): GenUniApp {
    return getUniApp() as GenUniApp
}
