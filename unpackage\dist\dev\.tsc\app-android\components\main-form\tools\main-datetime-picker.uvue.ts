
	import { DateQuickOption} from '@/components/main-form/form_type.uts'

	// 定义模式类型
	type DateTimeMode = 'datetime' | 'date' | 'time' | 'year' | 'year-month' | 'month' | 'day' | 'hour-minute' | 'hour-minute-second' | 'datetime-range' | 'date-range' | 'time-range'

	const __sfc__ = defineComponent({
		name: "main-datetime-picker",
		emits: ['cancel', 'confirm', 'change'],
		props: {
			// 选择模式
			mode: {
				type: String as PropType<DateTimeMode>,
				default: 'datetime' as DateTimeMode
			},
			// 标题
			title: {
				type: String,
				default: '选择时间'
			},
			// 是否显示秒
			showSeconds: {
				type: Boolean,
				default: false
			},
			// 开始年份
			startYear: {
				type: Number,
				default: () =>1970
			},
			// 结束年份
			endYear: {
				type: Number,
				default: () => 2035
			},
			// 快捷选项
			quickOptions: {
				type: Array as PropType<DateQuickOption[]>,
				default: () => [] as DateQuickOption[]
			},
			// 选择器高度
			height: {
				type: Number,
				default: 264
			}
		},
		data() {
			const now: Date = new Date()
			return {
				// 控制弹窗显示
				visible: false as boolean,
				// 当前日期
				currentDate: now as Date,
				// 区间值
				rangeValues: [now, now] as Date[],
				// 当前区间索引
				rangeIndex: 0 as number,
				// picker-view 的值
				pickerValue: [] as number[],
				// 当前快捷选项索引
				currentQuickIndex: -1 as number,
				// 年份列表
				years: [] as string[],
				// 月份列表
				months: [] as string[],
				// 日期列表
				days: [] as string[],
				// 小时列表
				hours: [] as string[],
				// 分钟列表
				minutes: [] as string[],
				// 秒列表
				seconds: [] as string[],
				// 是否已初始化
				isInitialized: false as boolean
			}
		},
		computed: {
			// 是否为区间模式
			isRange(): boolean {
				return this.mode.includes('range')
			},
			// 是否显示年份
			showYear(): boolean {
				return ['datetime', 'date', 'year', 'year-month'].includes(this.mode) ||
					   ['datetime-range', 'date-range'].includes(this.mode)
			},
			// 是否显示月份
			showMonth(): boolean {
				return ['datetime', 'date', 'year-month', 'month'].includes(this.mode) ||
					   ['datetime-range', 'date-range'].includes(this.mode)
			},
			// 是否显示日期
			showDay(): boolean {
				return ['datetime', 'date', 'day'].includes(this.mode) ||
					   ['datetime-range', 'date-range'].includes(this.mode)
			},
			// 是否显示小时
			showHour(): boolean {
				return ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||
					   ['datetime-range', 'time-range'].includes(this.mode)
			},
			// 是否显示分钟
			showMinute(): boolean {
				return ['datetime', 'time', 'hour-minute', 'hour-minute-second'].includes(this.mode) ||
					   ['datetime-range', 'time-range'].includes(this.mode)
			},
			// 是否显示秒
			showSecond(): boolean {
				return ['hour-minute-second'].includes(this.mode) ||
					   (this.showSeconds && ['datetime', 'time'].includes(this.mode)) ||
					   (this.showSeconds && ['datetime-range', 'time-range'].includes(this.mode))
			},
			// 指示器样式
			indicatorStyle(): string {
				return 'height: 44px; border-top: 1px solid #eee; border-bottom: 1px solid #eee;'
			},
			// 显示标题
			displayTitle(): string {
				if (this.title != '选择时间') return this.title

				const modeMap: UTSJSONObject = {__$originalPosition: new UTSSourceMapPosition("modeMap", "components/main-form/tools/main-datetime-picker.uvue", 201, 11),
					'datetime': '选择日期时间',
					'date': '选择日期',
					'time': '选择时间',
					'year': '选择年份',
					'year-month': '选择年月',
					'month': '选择月份',
					'day': '选择日期',
					'hour-minute': '选择时间',
					'hour-minute-second': '选择时间',
					'datetime-range': '选择日期时间范围',
					'date-range': '选择日期范围',
					'time-range': '选择时间范围'
				}

				const result = modeMap[this.mode] as string | null
				return result != null ? result : '选择时间'
			},
			// 当前显示值
			currentDisplayValue(): string {
				if (this.isRange) {
					const startFormatted: string = this.formatDate(this.rangeValues[0], this.mode.replace('-range', '') as DateTimeMode)
					const endFormatted: string = this.formatDate(this.rangeValues[1], this.mode.replace('-range', '') as DateTimeMode)
					return startFormatted + ' 至 ' + endFormatted
				} else {
					return this.formatDate(this.currentDate, this.mode)
				}
			}
		},
		created() {
			this.initData()
		},
		methods: {
			// 格式化日期
			formatDate(date: Date | null, type: DateTimeMode): string {
				if (date == null) return ''

				try {
					// 确保是 Date 对象
					const d: Date = date
					if (!this.validateDate(d)) return ''

					const year: number = d.getFullYear()
					const month: string = (d.getMonth() + 1).toString().padStart(2, '0')
					const day: string = d.getDate().toString().padStart(2, '0')
					const hour: string = d.getHours().toString().padStart(2, '0')
					const minute: string = d.getMinutes().toString().padStart(2, '0')
					const second: string = d.getSeconds().toString().padStart(2, '0')

					switch (type) {
						case 'datetime':
							return `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`
						case 'date':
							return `${year}-${month}-${day}`
						case 'time':
							return `${hour}:${minute}${this.showSeconds ? ':' + second : ''}`
						case 'year':
							return `${year}`
						case 'year-month':
							return `${year}-${month}`
						case 'month':
							return `${month}`
						case 'day':
							return `${day}`
						case 'hour-minute':
							return `${hour}:${minute}`
						case 'hour-minute-second':
							return `${hour}:${minute}:${second}`
						default:
							return `${year}-${month}-${day} ${hour}:${minute}${this.showSeconds ? ':' + second : ''}`
					}
				} catch (error) {
					console.error('Format date error:', error, date, " at components/main-form/tools/main-datetime-picker.uvue:273")
					return ''
				}
			},

			// 解析日期
			parseDate(value: Date | string | number | null): Date {
				if (value == null) return new Date()

				try {
					let date: Date | null = null
					if (value instanceof Date) {
						// 如果已经是 Date 对象，创建一个新的副本
						date = new Date(value.getTime())
					} else if (typeof value == 'number' && !isNaN(value)) {
						// 数字类型，作为时间戳处理
						date = new Date(value as number)
					} else if (typeof value == 'string') {
						// 字符串类型，需要解析
						if (value.includes('T')) {
							// ISO 格式字符串
							date = new Date(value as string)
						} else if (value.includes('-') || value.includes('/')) {
							// 自定义格式字符串，手动解析
							const parts: number[] = value.split(/[-\s:/]/).map(p => parseInt(p))
							if (parts.length >= 3) {
								date = new Date(
									parts[0], // year
									parts[1] - 1, // month
									parts[2], // day
									parts.length > 3 ? parts[3] : 0, // hour
									parts.length > 4 ? parts[4] : 0, // minute
									parts.length > 5 ? parts[5] : 0 // second
								)
							}
						} else {
							// 尝试作为时间戳字符串解析
							const timestamp: number = parseInt(value)
							if (!isNaN(timestamp)) {
								date = new Date(timestamp as number)
							}
						}
					}

					return date != null && !isNaN(date.getTime()) ? date : new Date()
				} catch (error) {
					console.error('Parse date error:', error, " at components/main-form/tools/main-datetime-picker.uvue:319")
					return new Date()
				}
			},

			// 验证日期
			validateDate(date: Date): boolean {
				if (isNaN(date.getTime())) {
					console.warn('Invalid date:', date, " at components/main-form/tools/main-datetime-picker.uvue:327")
					return false
				}

				const year: number = date.getFullYear()
				if (year < this.startYear || year > this.endYear) {
					console.warn('Date out of range:', date, " at components/main-form/tools/main-datetime-picker.uvue:333")
					return false
				}

				return true
			},

			// 显示选择器
			show(value?: Date | Date[] | string | number) {
				this.visible = true
				this.currentQuickIndex = -1
				this.rangeIndex = 0

				try {
					if (this.isRange) {
						// 处理区间值
						if (Array.isArray(value) && value.length == 2) {
							this.rangeValues = value.map(v => this.parseDate(v))
						} else if (typeof value == 'string') {
							// 尝试解析字符串格式的日期
							const date: Date = this.parseDate(value)
							this.rangeValues = [date, date]
						} else {
							const now: Date = new Date()
							this.rangeValues = [now, now]
						}
					} else {
						// 处理单个值
						this.currentDate = this.parseDate(value)
					}

					this.$nextTick(() => {
						this.initData()
						this.updateCurrentValue()
					})
				} catch (error) {
					console.error('Show picker error:', error, " at components/main-form/tools/main-datetime-picker.uvue:369")
					const now: Date = new Date()
					if (this.isRange) {
						this.rangeValues = [now, now]
					} else {
						this.currentDate = now
					}
				}
			},

			// 隐藏选择器
			hide() {
				this.visible = false
			},

			// 点击遮罩层关闭弹窗
			onOverlayClick() {
				this.hide()
				this.$emit('cancel')
			},

			// 取消按钮点击事件
			onCancel() {
				this.hide()
				this.$emit('cancel')
			},

			// 创建值的Map对象
			createValueMap(date: Date): Map<string, any> {
				const map = new Map<string, any>()

				if (this.showYear) {
					map.set('year', date.getFullYear())
				}
				if (this.showMonth) {
					map.set('month', date.getMonth() + 1)
				}
				if (this.showDay) {
					map.set('day', date.getDate())
				}
				if (this.showHour) {
					map.set('hour', date.getHours())
				}
				if (this.showMinute) {
					map.set('minute', date.getMinutes())
				}
				if (this.showSecond) {
					map.set('second', date.getSeconds())
				}

				return map
			},

			// 确定按钮点击事件
			onConfirm() {
				try {
					if (this.isRange) {
						if (!this.validateDate(this.rangeValues[0]) || !this.validateDate(this.rangeValues[1])) {
							uni.showToast({
								title: '日期格式无效',
								icon: 'none'
							})
							return
						}

						if (this.rangeValues[1] < this.rangeValues[0]) {
							uni.showToast({
								title: '结束时间不能早于开始时间',
								icon: 'none'
							})
							return
						}

						const dateValues: Date[] = this.rangeValues.map(date => new Date(date.getTime()))
						const mapValues: Map<string, any>[] = dateValues.map(date => this.createValueMap(date))
						const formatted: string = dateValues.map(date => this.formatDate(date, this.mode.replace('-range', '') as DateTimeMode)).join(' 至 ')

						this.$emit('confirm', {
							value: mapValues,
							formatted
						})
					} else {
						if (!this.validateDate(this.currentDate)) {
							uni.showToast({
								title: '日期格式无效',
								icon: 'none'
							})
							return
						}

						const dateValue: Date = new Date(this.currentDate.getTime())
						const mapValue: Map<string, any> = this.createValueMap(dateValue)
						const formatted: string = this.formatDate(dateValue, this.mode)

						this.$emit('confirm', {
							value: mapValue,
							formatted
						})
					}

					this.hide()
				} catch (error) {
					console.error('Confirm error:', error, " at components/main-form/tools/main-datetime-picker.uvue:471")
					uni.showToast({
						title: '操作失败',
						icon: 'none'
					})
				}
			},

			// 通过索引选择快捷选项
			onQuickSelectByIndex(index: number) {
				if (index < 0 || index >= this.quickOptions.length) {
					console.warn('Invalid quick option index:', index, " at components/main-form/tools/main-datetime-picker.uvue:482")
					return
				}

				const option: DateQuickOption = this.quickOptions[index]
				this.onQuickSelect(option, index)
			},

			// 快捷选项选择
			onQuickSelect(option: DateQuickOption, index: number) {
				this.currentQuickIndex = index
				this.rangeIndex = 0

				try {
					if (this.isRange) {
						// 处理区间值
						if (Array.isArray(option.value)) {
							// Date 数组
							const rangeValue: Date[] = option.value as Date[]
							if (rangeValue.length != 2) {
								console.warn('Quick option value should have 2 items for range mode:', option, " at components/main-form/tools/main-datetime-picker.uvue:502")
								return
							}
							this.rangeValues = rangeValue.map(v => this.parseDate(v))
						} else {
							// 单个 Date，转换为区间
							const date: Date = this.parseDate(option.value as Date)
							this.rangeValues = [date, date]
						}
					} else {
						// 处理单个值
						if (Array.isArray(option.value)) {
							// 如果是数组，取第一个值
							this.currentDate = this.parseDate((option.value as Date[])[0])
						} else {
							// 单个 Date 值
							this.currentDate = this.parseDate(option.value as Date)
						}
					}

					this.$nextTick(() => {
						this.initData()
						this.updateCurrentValue()
					})

					if (option.autoConfirm == true) {
						this.onConfirm()
					}
				} catch (error) {
					console.error('Quick select error:', error, " at components/main-form/tools/main-datetime-picker.uvue:531")
					uni.showToast({
						title: '快捷选项格式无效',
						icon: 'none'
					})
				}
			},

			// picker-view 变化事件
			onPickerChange(e: UniPickerViewChangeEvent) {
				this.pickerValue = e.detail.value
				this.currentQuickIndex = -1
				this.updateDateFromValue()
			},

			// 区间选择切换
			onRangeChange(index: number) {
				if (this.rangeIndex == index) return
				this.rangeIndex = index
				this.$nextTick(() => {
					this.updateCurrentValue()
				})
			},

			// 初始化数据
			initData() {
				if (!this.isInitialized) {
					// 年
					this.years = []
					for (let i: number = this.startYear; i <= this.endYear; i++) {
						this.years.push(i.toString())
					}

					// 月
					this.months = []
					for (let i: number = 1; i <= 12; i++) {
						this.months.push(i.toString().padStart(2, '0'))
					}

					// 时
					this.hours = []
					for (let i: number = 0; i <= 23; i++) {
						this.hours.push(i.toString().padStart(2, '0'))
					}

					// 分
					this.minutes = []
					for (let i: number = 0; i <= 59; i++) {
						this.minutes.push(i.toString().padStart(2, '0'))
					}

					// 秒
					this.seconds = []
					for (let i: number = 0; i <= 59; i++) {
						this.seconds.push(i.toString().padStart(2, '0'))
					}

					this.isInitialized = true
				}

				// 更新日期列表
				this.updateDaysList()
			},

			// 更新日期列表（根据当前年月动态计算）
			updateDaysList() {
				const date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate
				const year: number = date.getFullYear()
				const month: number = date.getMonth() + 1

				// 获取当月天数，处理闰年和不同月份的天数差异
				const daysInMonth: number = this.getDaysInMonth(year, month)

				this.days = []
				for (let i: number = 1; i <= daysInMonth; i++) {
					this.days.push(i.toString().padStart(2, '0'))
				}
			},

			// 获取指定年月的天数
			getDaysInMonth(year: number, month: number): number {
				// 使用 Date 构造函数的特性：月份设为下个月的第0天，会自动返回当月最后一天
				return new Date(year, month, 0).getDate()
			},

			// 更新当前值
			updateCurrentValue() {
				const date: Date = this.isRange ? this.rangeValues[this.rangeIndex] : this.currentDate
				if (isNaN(date.getTime())) {
					console.warn('Invalid date in updateCurrentValue:', date, " at components/main-form/tools/main-datetime-picker.uvue:620")
					return
				}

				// 确保日期列表是最新的
				this.updateDaysList()

				const values: number[] = []

				if (this.showYear) {
					const yearIndex: number = this.years.findIndex(y => parseInt(y) == date.getFullYear())
					values.push(yearIndex >= 0 ? yearIndex : 0)
				}

				if (this.showMonth) {
					const monthStr: string = (date.getMonth() + 1).toString().padStart(2, '0')
					const monthIndex: number = this.months.findIndex(m => m == monthStr)
					values.push(monthIndex >= 0 ? monthIndex : 0)
				}

				if (this.showDay) {
					const dayStr: string = date.getDate().toString().padStart(2, '0')
					const dayIndex: number = this.days.findIndex(d => d == dayStr)
					// 确保索引在有效范围内
					if (dayIndex >= 0 && dayIndex < this.days.length) {
						values.push(dayIndex)
					} else {
						// 如果找不到对应的日期，使用当月最后一天的索引
						values.push(this.days.length - 1)
					}
				}

				if (this.showHour) {
					const hourStr: string = date.getHours().toString().padStart(2, '0')
					const hourIndex: number = this.hours.findIndex(h => h == hourStr)
					values.push(hourIndex >= 0 ? hourIndex : 0)
				}

				if (this.showMinute) {
					const minuteStr: string = date.getMinutes().toString().padStart(2, '0')
					const minuteIndex: number = this.minutes.findIndex(m => m == minuteStr)
					values.push(minuteIndex >= 0 ? minuteIndex : 0)
				}

				if (this.showSecond) {
					const secondStr: string = date.getSeconds().toString().padStart(2, '0')
					const secondIndex: number = this.seconds.findIndex(s => s == secondStr)
					values.push(secondIndex >= 0 ? secondIndex : 0)
				}

				this.pickerValue = [...values]
			},

			// 从picker值更新日期
			updateDateFromValue() {
				if (!Array.isArray(this.pickerValue)) return

				let index: number = 0
				let year: number = this.currentDate.getFullYear()
				let month: number = this.currentDate.getMonth()
				let day: number = this.currentDate.getDate()
				let hour: number = this.currentDate.getHours()
				let minute: number = this.currentDate.getMinutes()
				let second: number = this.currentDate.getSeconds()

				// 记录原始的年月，用于检测是否发生变化
				const originalYear: number = year
				const originalMonth: number = month

				if (this.showYear && index < this.pickerValue.length) {
					year = parseInt(this.years[this.pickerValue[index]])
					index++
				}

				if (this.showMonth && index < this.pickerValue.length) {
					month = parseInt(this.months[this.pickerValue[index]]) - 1
					index++
				}

				// 检查年月是否发生变化，如果是，需要重新计算日期列表
				const yearMonthChanged: boolean = (year != originalYear || month != originalMonth)

				if (yearMonthChanged) {
					// 年月发生变化，需要重新计算当月天数
					const daysInNewMonth: number = this.getDaysInMonth(year, month + 1)

					// 如果当前日期超过了新月份的最大天数，调整为最大天数
					if (day > daysInNewMonth) {
						day = daysInNewMonth
					}

					// 更新日期列表
					this.days = []
					for (let i: number = 1; i <= daysInNewMonth; i++) {
						this.days.push(i.toString().padStart(2, '0'))
					}

					// 更新 picker-view 的日期索引，确保在有效范围内
					if (this.showDay && index < this.pickerValue.length) {
						const currentDayIndex: number = this.pickerValue[index]
						if (currentDayIndex >= this.days.length) {
							// 如果当前索引超出新月份的天数范围，调整为最后一天
							this.pickerValue[index] = this.days.length - 1
						}
					}
				}

				if (this.showDay && index < this.pickerValue.length) {
					const dayIndex: number = this.pickerValue[index]
					// 检查索引是否在有效范围内
					if (dayIndex >= 0 && dayIndex < this.days.length) {
						const selectedDay: number = parseInt(this.days[dayIndex])
						day = selectedDay
					} else {
						// 如果索引超出范围，使用当月最后一天
						day = this.days.length
					}
					index++
				}

				if (this.showHour && index < this.pickerValue.length) {
					hour = parseInt(this.hours[this.pickerValue[index]])
					index++
				}

				if (this.showMinute && index < this.pickerValue.length) {
					minute = parseInt(this.minutes[this.pickerValue[index]])
					index++
				}

				if (this.showSecond && index < this.pickerValue.length) {
					second = parseInt(this.seconds[this.pickerValue[index]])
				}

				const newDate: Date = new Date(year, month, day, hour, minute, second)

				if (this.isRange) {
					this.rangeValues[this.rangeIndex] = newDate
					if (this.rangeIndex == 0 && this.rangeValues[1] < newDate) {
						this.rangeValues[1] = new Date(newDate.getTime())
					}
				} else {
					this.currentDate = newDate
				}

				// 如果年月没有变化，只需要更新日期列表
				if (!yearMonthChanged) {
					this.updateDaysList()
				}
			}
		}
	})
	
export default __sfc__
function GenComponentsMainFormToolsMainDatetimePickerRender(this: InstanceType<typeof __sfc__>): any | null {
const _ctx = this
const _cache = this.$.renderCache
const _component_picker_view_column = resolveComponent("picker-view-column")
const _component_picker_view = resolveComponent("picker-view")

  return isTrue(_ctx.visible)
    ? _cE("view", _uM({
        key: 0,
        class: "picker-overlay",
        onClick: _ctx.onOverlayClick
      }), [
        _cE("view", _uM({
          class: "picker-modal",
          onClick: withModifiers(() => {}, ["stop"])
        }), [
          _cE("view", _uM({ class: "datetime-picker-container" }), [
            _cE("view", _uM({ class: "navbar" }), [
              _cE("text", _uM({
                class: "nav-btn cancel-btn",
                onClick: _ctx.onCancel
              }), "取消", 8 /* PROPS */, ["onClick"]),
              _cE("text", _uM({ class: "nav-title" }), _tD(_ctx.displayTitle), 1 /* TEXT */),
              _cE("view", _uM({ class: "confirm-btn-container" }), [
                _cE("text", _uM({
                  class: "nav-btn confirm-btn",
                  onClick: _ctx.onConfirm
                }), "确定", 8 /* PROPS */, ["onClick"])
              ])
            ]),
            _ctx.quickOptions.length > 0
              ? _cE("view", _uM({
                  key: 0,
                  class: "quick-options-container"
                }), [
                  _cE("scroll-view", _uM({
                    direction: "horizontal",
                    class: "quick-options-scroll",
                    "show-scrollbar": "false"
                  }), [
                    _cE("view", _uM({ class: "quick-options" }), [
                      _cE(Fragment, null, RenderHelpers.renderList(_ctx.quickOptions, (option, index, __index, _cached): any => {
                        return _cE("text", _uM({
                          key: index,
                          class: _nC(["quick-item", _uM({ 'quick-item-active': _ctx.currentQuickIndex == index })]),
                          onClick: () => {_ctx.onQuickSelectByIndex(index)}
                        }), _tD(option.label), 11 /* TEXT, CLASS, PROPS */, ["onClick"])
                      }), 128 /* KEYED_FRAGMENT */)
                    ])
                  ])
                ])
              : _cC("v-if", true),
            isTrue(_ctx.isRange)
              ? _cE("view", _uM({
                  key: 1,
                  class: "range-tabs"
                }), [
                  _cE("text", _uM({
                    class: _nC(["range-tab", _uM({ 'range-tab-active': _ctx.rangeIndex == 0 })]),
                    onClick: () => {_ctx.onRangeChange(0)}
                  }), " 开始时间 ", 10 /* CLASS, PROPS */, ["onClick"]),
                  _cE("text", _uM({
                    class: _nC(["range-tab", _uM({ 'range-tab-active': _ctx.rangeIndex == 1 })]),
                    onClick: () => {_ctx.onRangeChange(1)}
                  }), " 结束时间 ", 10 /* CLASS, PROPS */, ["onClick"])
                ])
              : _cC("v-if", true),
            _cE("view", _uM({ class: "picker-body" }), [
              _cV(_component_picker_view, _uM({
                value: _ctx.pickerValue,
                onChange: _ctx.onPickerChange,
                class: "picker-view",
                "indicator-style": _ctx.indicatorStyle,
                style: _nS(_uM({ height: `${_ctx.height}px` }))
              }), _uM({
                default: withSlotCtx((): any[] => [
                  isTrue(_ctx.showYear)
                    ? _cV(_component_picker_view_column, _uM({ key: 0 }), _uM({
                        default: withSlotCtx((): any[] => [
                          _cE(Fragment, null, RenderHelpers.renderList(_ctx.years, (year, __key, __index, _cached): any => {
                            return _cE("view", _uM({
                              class: "picker-item",
                              key: year
                            }), [
                              _cE("text", _uM({ class: "picker-text" }), _tD(year) + "年", 1 /* TEXT */)
                            ])
                          }), 128 /* KEYED_FRAGMENT */)
                        ]),
                        _: 1 /* STABLE */
                      }))
                    : _cC("v-if", true),
                  isTrue(_ctx.showMonth)
                    ? _cV(_component_picker_view_column, _uM({ key: 1 }), _uM({
                        default: withSlotCtx((): any[] => [
                          _cE(Fragment, null, RenderHelpers.renderList(_ctx.months, (month, __key, __index, _cached): any => {
                            return _cE("view", _uM({
                              class: "picker-item",
                              key: month
                            }), [
                              _cE("text", _uM({ class: "picker-text" }), _tD(month) + "月", 1 /* TEXT */)
                            ])
                          }), 128 /* KEYED_FRAGMENT */)
                        ]),
                        _: 1 /* STABLE */
                      }))
                    : _cC("v-if", true),
                  isTrue(_ctx.showDay)
                    ? _cV(_component_picker_view_column, _uM({ key: 2 }), _uM({
                        default: withSlotCtx((): any[] => [
                          _cE(Fragment, null, RenderHelpers.renderList(_ctx.days, (day, __key, __index, _cached): any => {
                            return _cE("view", _uM({
                              class: "picker-item",
                              key: day
                            }), [
                              _cE("text", _uM({ class: "picker-text" }), _tD(day) + "日", 1 /* TEXT */)
                            ])
                          }), 128 /* KEYED_FRAGMENT */)
                        ]),
                        _: 1 /* STABLE */
                      }))
                    : _cC("v-if", true),
                  isTrue(_ctx.showHour)
                    ? _cV(_component_picker_view_column, _uM({ key: 3 }), _uM({
                        default: withSlotCtx((): any[] => [
                          _cE(Fragment, null, RenderHelpers.renderList(_ctx.hours, (hour, __key, __index, _cached): any => {
                            return _cE("view", _uM({
                              class: "picker-item",
                              key: hour
                            }), [
                              _cE("text", _uM({ class: "picker-text" }), _tD(hour) + "时", 1 /* TEXT */)
                            ])
                          }), 128 /* KEYED_FRAGMENT */)
                        ]),
                        _: 1 /* STABLE */
                      }))
                    : _cC("v-if", true),
                  isTrue(_ctx.showMinute)
                    ? _cV(_component_picker_view_column, _uM({ key: 4 }), _uM({
                        default: withSlotCtx((): any[] => [
                          _cE(Fragment, null, RenderHelpers.renderList(_ctx.minutes, (minute, __key, __index, _cached): any => {
                            return _cE("view", _uM({
                              class: "picker-item",
                              key: minute
                            }), [
                              _cE("text", _uM({ class: "picker-text" }), _tD(minute) + "分", 1 /* TEXT */)
                            ])
                          }), 128 /* KEYED_FRAGMENT */)
                        ]),
                        _: 1 /* STABLE */
                      }))
                    : _cC("v-if", true),
                  isTrue(_ctx.showSecond)
                    ? _cV(_component_picker_view_column, _uM({ key: 5 }), _uM({
                        default: withSlotCtx((): any[] => [
                          _cE(Fragment, null, RenderHelpers.renderList(_ctx.seconds, (second, __key, __index, _cached): any => {
                            return _cE("view", _uM({
                              class: "picker-item",
                              key: second
                            }), [
                              _cE("text", _uM({ class: "picker-text" }), _tD(second) + "秒", 1 /* TEXT */)
                            ])
                          }), 128 /* KEYED_FRAGMENT */)
                        ]),
                        _: 1 /* STABLE */
                      }))
                    : _cC("v-if", true)
                ]),
                _: 1 /* STABLE */
              }), 8 /* PROPS */, ["value", "onChange", "indicator-style", "style"])
            ])
          ])
        ], 8 /* PROPS */, ["onClick"])
      ], 8 /* PROPS */, ["onClick"])
    : _cC("v-if", true)
}
const GenComponentsMainFormToolsMainDatetimePickerStyles = [_uM([["picker-overlay", _pS(_uM([["position", "fixed"], ["top", 0], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "rgba(0,0,0,0.4)"], ["zIndex", 999]]))], ["picker-modal", _pS(_uM([["position", "fixed"], ["left", 0], ["right", 0], ["bottom", 0], ["backgroundColor", "#ffffff"], ["zIndex", 1000]]))], ["datetime-picker-container", _pS(_uM([["width", "100%"], ["backgroundColor", "#ffffff"], ["display", "flex"], ["flexDirection", "column"]]))], ["navbar", _pS(_uM([["display", "flex"], ["alignItems", "center"], ["flexDirection", "row"], ["justifyContent", "space-between"], ["height", "88rpx"], ["paddingTop", 0], ["paddingRight", "30rpx"], ["paddingBottom", 0], ["paddingLeft", "30rpx"], ["backgroundColor", "#ffffff"], ["borderBottomWidth", "1rpx"], ["borderBottomStyle", "solid"], ["borderBottomColor", "#eeeeee"]]))], ["nav-btn", _pS(_uM([["minWidth", "80rpx"], ["textAlign", "center"]]))], ["cancel-btn", _pS(_uM([["color", "#999999"], ["fontSize", "32rpx"]]))], ["confirm-btn-container", _pS(_uM([["minWidth", "80rpx"], ["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"]]))], ["confirm-btn", _pS(_uM([["color", "#576B95"], ["fontWeight", "400"], ["fontSize", "32rpx"]]))], ["nav-title", _pS(_uM([["color", "#333333"], ["fontWeight", "400"], ["fontSize", "32rpx"], ["textAlign", "center"], ["flex", 1]]))], ["quick-options-container", _pS(_uM([["borderBottomWidth", "1rpx"], ["borderBottomStyle", "solid"], ["borderBottomColor", "#eeeeee"]]))], ["quick-options-scroll", _pS(_uM([["height", "80rpx"], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"]]))], ["quick-options", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["paddingTop", "10rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "10rpx"], ["paddingLeft", "20rpx"], ["height", "60rpx"]]))], ["quick-item", _pS(_uM([["paddingTop", "8rpx"], ["paddingRight", "16rpx"], ["paddingBottom", "8rpx"], ["paddingLeft", "16rpx"], ["marginRight", "12rpx"], ["fontSize", "24rpx"], ["color", "#666666"], ["backgroundColor", "#f5f5f5"], ["borderTopLeftRadius", "20rpx"], ["borderTopRightRadius", "20rpx"], ["borderBottomRightRadius", "20rpx"], ["borderBottomLeftRadius", "20rpx"], ["whiteSpace", "nowrap"]]))], ["quick-item-active", _pS(_uM([["color", "#ffffff"], ["backgroundColor", "#007AFF"]]))], ["range-tabs", _pS(_uM([["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["paddingTop", "20rpx"], ["paddingRight", "20rpx"], ["paddingBottom", "20rpx"], ["paddingLeft", "20rpx"], ["borderBottomWidth", "1rpx"], ["borderBottomStyle", "solid"], ["borderBottomColor", "#eeeeee"]]))], ["range-tab", _pS(_uM([["flex", 1], ["textAlign", "center"], ["fontSize", "28rpx"], ["color", "#666666"], ["paddingTop", "10rpx"], ["paddingRight", 0], ["paddingBottom", "10rpx"], ["paddingLeft", 0]]))], ["range-tab-active", _pS(_uM([["color", "#007AFF"], ["position", "relative"]]))], ["picker-body", _pS(_uM([["position", "relative"]]))], ["picker-view", _pS(_uM([["width", "100%"], ["height", 264]]))], ["picker-item", _pS(_uM([["display", "flex"], ["justifyContent", "center"], ["alignItems", "center"], ["height", 44], ["overflow", "hidden"]]))], ["picker-text", _pS(_uM([["fontSize", 16], ["color", "#333333"]]))]])]
